#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于等待表的增量处理方案 - 简单增量处理器
版本: 1.0
创建时间: 2025-08-01

核心思路：
- 只记录不完整订单，完整订单直接进入分析
- 在数据预处理阶段解决增量问题
- 复用现有的position构建和完整性判断逻辑
"""

import os
import sys
import time
import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from enum import Enum

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))
sys.path.insert(0, os.path.join(project_root, 'backend'))

from database.duckdb_manager import DuckDBManager
from modules.contract_risk_analysis.optimizers.position_based_optimizer import (
    PositionBasedOptimizer, CompletePosition
)

# 导入配置加载器
from .incremental_config_loader import config_loader, IncrementalProcessingConfig

# 配置日志
logger = logging.getLogger(__name__)

class ProcessingErrorType(Enum):
    """处理错误类型枚举"""
    DATA_VALIDATION_ERROR = "data_validation_error"
    DATABASE_CONNECTION_ERROR = "database_connection_error"
    DATABASE_OPERATION_ERROR = "database_operation_error"
    POSITION_BUILDING_ERROR = "position_building_error"
    MATCHING_LOGIC_ERROR = "matching_logic_error"
    DATA_MERGE_ERROR = "data_merge_error"
    CONFIGURATION_ERROR = "configuration_error"
    UNKNOWN_ERROR = "unknown_error"

class IncrementalProcessingError(Exception):
    """增量处理专用异常类"""

    def __init__(self, error_type: ProcessingErrorType, message: str,
                 details: Dict[str, Any] = None, original_exception: Exception = None):
        self.error_type = error_type
        self.message = message
        self.details = details or {}
        self.original_exception = original_exception
        self.timestamp = datetime.now()

        super().__init__(f"[{error_type.value}] {message}")

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式，便于日志记录"""
        return {
            'error_type': self.error_type.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp.isoformat(),
            'original_exception': str(self.original_exception) if self.original_exception else None
        }

class ErrorHandler:
    """错误处理器"""

    def __init__(self, task_id: str):
        self.task_id = task_id
        self.error_count = 0
        self.error_history = []
        self.max_error_history = 100

    def handle_error(self, error: IncrementalProcessingError,
                    context: str = "", should_raise: bool = False) -> bool:
        """
        处理错误

        Args:
            error: 增量处理错误对象
            context: 错误上下文信息
            should_raise: 是否重新抛出异常

        Returns:
            bool: 是否应该继续处理
        """
        self.error_count += 1

        # 记录错误历史
        error_record = {
            'context': context,
            'error': error.to_dict(),
            'sequence': self.error_count
        }

        self.error_history.append(error_record)

        # 保持错误历史在合理范围内
        if len(self.error_history) > self.max_error_history:
            self.error_history.pop(0)

        # 根据错误类型决定日志级别和处理策略
        if error.error_type in [ProcessingErrorType.DATA_VALIDATION_ERROR,
                               ProcessingErrorType.CONFIGURATION_ERROR]:
            logger.error(f"严重错误 [{self.task_id}] {context}: {error}")
            if should_raise:
                raise error
            return False  # 停止处理

        elif error.error_type in [ProcessingErrorType.DATABASE_CONNECTION_ERROR]:
            logger.error(f"数据库连接错误 [{self.task_id}] {context}: {error}")
            if should_raise:
                raise error
            return False  # 停止处理

        elif error.error_type in [ProcessingErrorType.DATABASE_OPERATION_ERROR,
                                 ProcessingErrorType.MATCHING_LOGIC_ERROR,
                                 ProcessingErrorType.DATA_MERGE_ERROR]:
            logger.warning(f"可恢复错误 [{self.task_id}] {context}: {error}")
            return True  # 继续处理

        else:
            logger.error(f"未知错误 [{self.task_id}] {context}: {error}")
            if should_raise:
                raise error
            return False  # 停止处理

    def get_error_summary(self) -> Dict[str, Any]:
        """获取错误摘要"""
        error_types = {}
        for record in self.error_history:
            error_type = record['error']['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1

        return {
            'task_id': self.task_id,
            'total_errors': self.error_count,
            'error_types': error_types,
            'recent_errors': self.error_history[-10:] if self.error_history else []
        }

    @property
    def error_types(self) -> Dict[str, int]:
        """获取错误类型统计"""
        error_types = {}
        for record in self.error_history:
            error_type = record['error']['error_type']
            error_types[error_type] = error_types.get(error_type, 0) + 1
        return error_types

    def _should_retry(self, error: IncrementalProcessingError, attempt: int, max_retries: int) -> bool:
        """判断是否应该重试"""
        if attempt > max_retries:
            return False

        # 根据错误类型决定是否重试
        retryable_errors = [
            ProcessingErrorType.DATABASE_OPERATION_ERROR,
            ProcessingErrorType.MATCHING_LOGIC_ERROR,
            ProcessingErrorType.DATA_MERGE_ERROR
        ]

        non_retryable_errors = [
            ProcessingErrorType.DATA_VALIDATION_ERROR,
            ProcessingErrorType.CONFIGURATION_ERROR,
            ProcessingErrorType.DATABASE_CONNECTION_ERROR
        ]

        if error.error_type in non_retryable_errors:
            return False

        return error.error_type in retryable_errors

class SimpleIncrementalProcessor:
    """基于等待表的简单增量处理器"""

    def __init__(self, task_id: str = None, config: Optional[IncrementalProcessingConfig] = None):
        self.task_id = task_id or f"task_{int(time.time())}"

        # 加载配置
        if config is not None:
            self.config = config
        else:
            # 使用默认配置路径
            default_config_path = os.path.join(
                os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(__file__)))),
                'config', 'incremental_processing_config.yml'
            )
            self.config = config_loader.load_config(default_config_path)

        self.optimizer = PositionBasedOptimizer()  # 复用现有构建器
        self.error_handler = ErrorHandler(self.task_id)  # 错误处理器

        # 数据库连接初始化（带错误处理）
        try:
            self.db_manager = DuckDBManager()
        except Exception as e:
            error = IncrementalProcessingError(
                ProcessingErrorType.DATABASE_CONNECTION_ERROR,
                "数据库连接初始化失败",
                {"task_id": self.task_id},
                e
            )
            self.error_handler.handle_error(error, "初始化", should_raise=True)

        # 从配置中获取参数
        self.max_waiting_days = self.config.waiting_table.max_waiting_days
        self.max_check_count = self.config.waiting_table.max_check_count
        self.batch_size = getattr(self.config.performance, 'batch_size', 1000)
        self.waiting_table_name = getattr(self.config.database, 'waiting_table_name', 'incomplete_positions_waiting')

        # 性能统计
        self.stats = {
            'total_processed': 0,
            'completed_from_waiting': 0,
            'new_incomplete': 0,
            'processing_time': 0.0,
            'error_count': 0,
            'warning_count': 0
        }

    def process_new_data(self, new_df: pd.DataFrame) -> Dict[str, CompletePosition]:
        """
        处理新数据的核心逻辑

        返回: 所有完整的订单（可直接进入分析流程）
        """
        start_time = time.time()
        logger.info(f"🚀 开始增量处理，任务ID: {self.task_id}，数据量: {len(new_df)}")

        try:
            # 数据验证
            if not self._validate_input_data(new_df):
                return self._build_empty_result()

            # 1. 🚀 增量模式：完全跳过位置优化器，直接处理原始数据
            logger.info(f"🚀 增量模式：基于真实数据创建订单片段，准备与历史数据补齐")
            try:
                new_complete_positions = self._create_raw_position_fragments(new_df)
                logger.info(f"✅ 订单片段创建成功，共 {len(new_complete_positions)} 个片段")
            except Exception as e:
                logger.error(f"❌ 创建订单片段失败: {e}")
                import traceback
                logger.error(f"详细错误信息: {traceback.format_exc()}")
                error = IncrementalProcessingError(
                    ProcessingErrorType.POSITION_BUILDING_ERROR,
                    "创建订单片段失败",
                    {"data_size": len(new_df)},
                    e
                )
                if not self.error_handler.handle_error(error, "订单片段创建"):
                    return self._build_empty_result()
                new_complete_positions = {}

            # 2. 分离完整和不完整的订单
            try:
                complete_positions, incomplete_positions = self._separate_positions(new_complete_positions)
                logger.info(f"✅ 订单分离完成 - 完整: {len(complete_positions)}, 不完整: {len(incomplete_positions)}")
            except Exception as e:
                error = IncrementalProcessingError(
                    ProcessingErrorType.MATCHING_LOGIC_ERROR,
                    "订单分离失败",
                    {"positions_count": len(new_complete_positions)},
                    e
                )
                if not self.error_handler.handle_error(error, "订单分离"):
                    return self._build_empty_result()
                complete_positions, incomplete_positions = {}, {}

            # 3. 从等待表读取历史数据，与新数据进行真实补齐
            try:
                completed_from_waiting = self._try_complete_waiting_positions(new_complete_positions)
                logger.info(f"✅ 历史数据补齐完成，从等待表成功补齐订单数量: {len(completed_from_waiting)}")
            except Exception as e:
                error = IncrementalProcessingError(
                    ProcessingErrorType.MATCHING_LOGIC_ERROR,
                    "历史数据补齐失败",
                    {"new_positions_count": len(new_complete_positions)},
                    e
                )
                if not self.error_handler.handle_error(error, "历史数据补齐"):
                    completed_from_waiting = {}
                else:
                    completed_from_waiting = {}

            # 4. 更新等待表
            try:
                self._update_waiting_table(incomplete_positions, completed_from_waiting)
                logger.info(f"✅ 等待表更新完成")
            except Exception as e:
                error = IncrementalProcessingError(
                    ProcessingErrorType.DATABASE_OPERATION_ERROR,
                    "等待表更新失败",
                    {"incomplete_count": len(incomplete_positions), "completed_count": len(completed_from_waiting)},
                    e
                )
                self.error_handler.handle_error(error, "等待表更新")  # 不中断流程

            # 5. 合并所有完整订单
            all_complete_positions = {**complete_positions, **completed_from_waiting}

            # 6. 更新统计信息
            self._update_stats(len(new_complete_positions), len(completed_from_waiting),
                             len(incomplete_positions), time.time() - start_time,
                             self.error_handler.error_count)

            logger.info(f"🎉 增量处理完成，最终完整订单: {len(all_complete_positions)} 个，"
                       f"耗时: {self.stats['processing_time']:.2f}秒，错误数: {self.error_handler.error_count}")

            return all_complete_positions

        except IncrementalProcessingError as e:
            # 已经被错误处理器处理过的异常
            logger.error(f"❌ 增量处理失败: {e}")
            return self._build_empty_result()
        except Exception as e:
            # 未预期的异常
            error = IncrementalProcessingError(
                ProcessingErrorType.UNKNOWN_ERROR,
                "增量处理过程中发生未知错误",
                {"task_id": self.task_id},
                e
            )
            self.error_handler.handle_error(error, "主处理流程")
            return self._build_empty_result()

    def _validate_input_data(self, df: pd.DataFrame) -> bool:
        """
        验证输入数据的有效性

        Args:
            df: 输入数据框

        Returns:
            bool: 数据是否有效
        """
        try:
            # 数据为空检查
            if df.empty:
                logger.warning("⚠️  输入数据为空，跳过增量处理")
                return False

            # 🚀 修复：必要字段检查，时间字段支持多种名称
            required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'deal_vol', 'deal_vol_usdt']
            missing_fields = [field for field in required_fields if field not in df.columns]

            # 检查时间字段（支持多种名称）
            time_fields = ['timestamp', 'create_time', 'order_create_time']
            has_time_field = any(field in df.columns for field in time_fields)

            if not has_time_field:
                missing_fields.append('时间字段(timestamp/create_time/order_create_time)')

            if missing_fields:
                error = IncrementalProcessingError(
                    ProcessingErrorType.DATA_VALIDATION_ERROR,
                    f"输入数据缺少必要字段: {missing_fields}",
                    {"missing_fields": missing_fields, "available_fields": list(df.columns)}
                )
                self.error_handler.handle_error(error, "数据验证", should_raise=True)
                return False

            # 数据类型检查
            if not pd.api.types.is_numeric_dtype(df['deal_vol_usdt']):
                logger.warning("⚠️  deal_vol_usdt字段不是数值类型，尝试转换")
                try:
                    df['deal_vol_usdt'] = pd.to_numeric(df['deal_vol_usdt'], errors='coerce')
                except Exception as e:
                    error = IncrementalProcessingError(
                        ProcessingErrorType.DATA_VALIDATION_ERROR,
                        "deal_vol_usdt字段类型转换失败",
                        {"field": "deal_vol_usdt"},
                        e
                    )
                    self.error_handler.handle_error(error, "数据验证", should_raise=True)
                    return False

            # 数据量检查
            if len(df) > 1000000:  # 100万条记录
                logger.warning(f"⚠️  数据量过大: {len(df)} 条记录，可能影响性能")

            logger.info(f"✅ 数据验证通过，数据量: {len(df)} 条记录")
            return True

        except Exception as e:
            error = IncrementalProcessingError(
                ProcessingErrorType.DATA_VALIDATION_ERROR,
                "数据验证过程中发生错误",
                {"data_shape": df.shape if df is not None else None},
                e
            )
            self.error_handler.handle_error(error, "数据验证")
            return False

    def _build_empty_result(self) -> Dict[str, CompletePosition]:
        """构建空结果"""
        return {}

    def _separate_positions(self, positions: Dict[str, CompletePosition]) -> Tuple[Dict, Dict]:
        """
        🚀 增量模式：严格按照原始is_completed标记分离订单

        增量模式的核心理念：
        1. 所有新创建的片段都标记为is_completed=False
        2. 只有通过等待表匹配的历史数据才标记为完整
        3. 不在此阶段重新判断完整性，保持数据片段的原始状态
        """
        complete = {}
        incomplete = {}

        for pos_id, position in positions.items():
            # 🚀 关键修复：严格按照原始is_completed标记分离
            # 增量模式下，新创建的片段都是is_completed=False，应该进入等待表
            if position.is_completed:
                complete[pos_id] = position
                logger.debug(f"订单 {pos_id}: 已标记为完整（来自历史数据匹配）")
            else:
                incomplete[pos_id] = position
                logger.debug(f"订单 {pos_id}: 标记为不完整，将进入等待表")

        return complete, incomplete

    def _is_position_truly_completed(self, position: CompletePosition) -> bool:
        """
        基于deal_vol精确匹配的完整性判断逻辑

        核心思路：
        1. 基于deal_vol_usdt进行精确数量匹配
        2. 支持多层次容差判断
        3. 区分完整、部分、异常三种状态

        Args:
            position: CompletePosition对象

        Returns:
            bool: 是否完整
        """

        # 1. 基础检查：是否有平仓记录
        if position.close_trades_count == 0 or position.total_close_amount == 0:
            logger.debug(f"订单 {position.position_id}: 无平仓记录，判定为不完整")
            return False

        # 2. 开仓数量有效性检查
        if position.total_open_amount <= 0:
            logger.warning(f"订单 {position.position_id}: 开仓数量异常 {position.total_open_amount}")
            return False

        # 3. 基于deal_vol的精确数量匹配判断
        open_amount = position.total_open_amount
        close_amount = position.total_close_amount
        vol_diff = abs(open_amount - close_amount)

        # 多层次容差设计（从配置中获取）
        # 第一层：绝对精确匹配（适用于小额交易）
        absolute_tolerance = self.config.matching.tolerance.absolute

        # 第二层：相对容差匹配（适用于大额交易）
        relative_tolerance = self.config.matching.tolerance.relative
        relative_diff = vol_diff / open_amount if open_amount > 0 else float('inf')

        # 判断逻辑
        if vol_diff <= absolute_tolerance:
            # 绝对精确匹配
            logger.debug(f"订单 {position.position_id}: 绝对精确匹配 - 开仓{open_amount}, 平仓{close_amount}, 差异{vol_diff}")
            return True
        elif relative_diff <= relative_tolerance:
            # 相对精确匹配
            logger.debug(f"订单 {position.position_id}: 相对精确匹配 - 开仓{open_amount}, 平仓{close_amount}, 相对差异{relative_diff:.6f}")
            return True
        elif close_amount < open_amount:
            # 部分平仓，明确不完整
            completion_ratio = close_amount / open_amount
            logger.debug(f"订单 {position.position_id}: 部分平仓 - 开仓{open_amount}, 平仓{close_amount}, 完成度{completion_ratio:.2%}")
            return False
        else:
            # 过度平仓，可能是数据异常或者多次加仓
            over_ratio = close_amount / open_amount

            # 如果过度平仓超过10%，可能是数据异常
            if over_ratio > 1.1:
                logger.warning(f"订单 {position.position_id}: 严重过度平仓 - 开仓{open_amount}, 平仓{close_amount}, 比例{over_ratio:.2%}")
                # 虽然异常，但认为是完整的（已经全部平仓）
                return True
            else:
                # 轻微过度平仓，认为完整
                logger.debug(f"订单 {position.position_id}: 轻微过度平仓 - 开仓{open_amount}, 平仓{close_amount}, 比例{over_ratio:.2%}")
                return True

    def _try_complete_waiting_positions(self, new_positions: Dict) -> Dict[str, CompletePosition]:
        """
        从等待表读取历史真实数据，与新数据进行补齐合并
        """
        completed = {}

        try:
            # 获取等待中的订单
            waiting_positions = self._get_waiting_positions()
            logger.info(f"📖 从等待表读取历史真实数据: {len(waiting_positions)} 个订单")

            for waiting_pos in waiting_positions:
                try:
                    # 查找匹配的完整订单
                    matching_position = self._find_matching_complete_position(waiting_pos, new_positions)

                    if matching_position:
                        # 合并历史开仓数据和新的完整数据
                        complete_pos = self._merge_waiting_and_new_data(waiting_pos, matching_position)
                        completed[waiting_pos['position_id']] = complete_pos

                        logger.debug(f"✅ 真实数据补齐成功: {waiting_pos['position_id']} (历史开仓 + 新平仓)")
                except Exception as e:
                    logger.error(f"处理等待订单失败 {waiting_pos.get('position_id', 'unknown')}: {e}")
                    continue

        except Exception as e:
            error = IncrementalProcessingError(
                ProcessingErrorType.MATCHING_LOGIC_ERROR,
                "历史数据补齐失败",
                {"waiting_positions_count": len(waiting_positions) if 'waiting_positions' in locals() else 0},
                e
            )
            self.error_handler.handle_error(error, "历史数据补齐")

        return completed

    def _get_waiting_positions(self) -> List[Dict]:
        """获取等待中的订单"""
        try:
            sql = """
            SELECT position_id, member_id, contract_name, primary_side,
                   first_open_time, total_open_amount, open_trades_count,
                   avg_open_price, total_open_volume, waiting_since,
                   last_check_time, check_count
            FROM incomplete_positions_waiting
            ORDER BY waiting_since ASC
            """
            
            results = self.db_manager.execute_sql(sql)
            logger.debug(f"获取等待订单: {len(results)} 个")
            return results
            
        except Exception as e:
            logger.error(f"获取等待订单失败: {e}")
            return []

    def _find_matching_complete_position(self, waiting_pos: dict, new_positions: Dict) -> Optional[CompletePosition]:
        """
        精确匹配算法：基于position_id完全匹配查找完整订单

        🚀 修复：增量模式中新数据可能只包含平仓数据，不要求新订单本身完整
        """

        # position_id完全匹配
        if waiting_pos['position_id'] in new_positions:
            new_pos = new_positions[waiting_pos['position_id']]

            # 🚀 修复：不检查新订单本身是否完整，而是检查是否能与等待表数据组合
            # 只要新订单有平仓数据，就可以与等待表的开仓数据组合
            if new_pos.total_close_amount > 0:  # 新订单有平仓数据
                # 进一步验证数量匹配
                if self._validate_quantity_match(waiting_pos, new_pos):
                    logger.debug(f"找到精确匹配: {waiting_pos['position_id']} (等待表开仓 + 新数据平仓)")
                    return new_pos
                else:
                    logger.warning(f"position_id匹配但数量不匹配: {waiting_pos['position_id']}")
            else:
                logger.debug(f"position_id匹配但新订单无平仓数据: {waiting_pos['position_id']}")

        return None

    def _validate_quantity_match(self, waiting_pos: dict, new_pos: CompletePosition) -> bool:
        """
        验证等待订单和新订单的数量匹配关系

        🚀 修复：增量模式中新数据可能只包含平仓数据，开仓数量为0是正常的
        """

        # position_id相同，应该是同一个订单的补全
        if waiting_pos['position_id'] == new_pos.position_id:
            # 等待表中的开仓数量（转换为float类型）
            waiting_open_amount = float(waiting_pos['total_open_amount'])

            # 新订单中的开仓和平仓数量
            new_open_amount = float(new_pos.total_open_amount)
            new_close_amount = float(new_pos.total_close_amount)

            # 情况1：新数据包含开仓数据，验证开仓数量匹配
            if new_open_amount > 0 and waiting_open_amount > 0:
                diff_ratio = abs(waiting_open_amount - new_open_amount) / waiting_open_amount
                tolerance = 0.01  # 1%误差
                return diff_ratio <= tolerance

            # 情况2：新数据只包含平仓数据（增量模式常见情况）
            # 验证平仓数量与等待表开仓数量的合理性
            elif new_open_amount == 0 and new_close_amount > 0 and waiting_open_amount > 0:
                # 平仓数量应该与开仓数量相近（允许部分平仓或加仓情况）
                diff_ratio = abs(waiting_open_amount - new_close_amount) / waiting_open_amount
                tolerance = 0.5  # 50%误差，允许部分平仓或加仓
                return diff_ratio <= tolerance

            # 情况3：基本合理性检查
            return waiting_open_amount > 0  # 等待表必须有开仓数据

        return False

    def _merge_waiting_and_new_data(self, waiting_pos: dict, new_pos: CompletePosition) -> CompletePosition:
        """
        合并等待表中的历史开仓数据和新数据中的完整订单信息
        """
        
        # 创建合并后的完整订单（优先使用等待表中的历史开仓数据）
        merged_position = CompletePosition(
            position_id=waiting_pos['position_id'],
            member_id=waiting_pos['member_id'],
            contract_name=waiting_pos['contract_name'],

            # 开仓信息：优先使用等待表中的历史数据（转换为float类型）
            first_open_time=waiting_pos['first_open_time'],
            last_open_time=waiting_pos['first_open_time'],  # 简化处理
            total_open_amount=float(waiting_pos['total_open_amount']),
            total_open_volume=float(waiting_pos['total_open_volume']),
            avg_open_price=float(waiting_pos['avg_open_price']),
            open_trades_count=int(waiting_pos['open_trades_count']),
            primary_side=int(waiting_pos['primary_side']),
            
            # 平仓信息：使用新数据
            first_close_time=new_pos.first_close_time,
            last_close_time=new_pos.last_close_time,
            total_close_amount=new_pos.total_close_amount,
            total_close_volume=new_pos.total_close_volume,
            avg_close_price=new_pos.avg_close_price,
            close_trades_count=new_pos.close_trades_count,
            
            # 状态和计算字段
            is_completed=True,  # 合并后必然是完整的
            total_duration_minutes=self._calculate_duration(waiting_pos['first_open_time'], new_pos.last_close_time),
            real_profit=new_pos.real_profit,
            calculated_profit=new_pos.calculated_profit,
            
            # 其他字段使用新数据或默认值
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=0,
            reduce_position_count=new_pos.close_trades_count,
            risk_score=0.0,
            abnormal_flags=[],
            leverage=new_pos.leverage,
            total_fee=new_pos.total_fee,
            
            # 订单类型统计（使用新数据）
            market_orders_open=0,
            limit_orders_open=0,
            market_orders_close=new_pos.market_orders_close,
            limit_orders_close=new_pos.limit_orders_close,
            
            # 仓位模式（使用新数据）
            cross_margin_positions=new_pos.cross_margin_positions,
            isolated_margin_positions=new_pos.isolated_margin_positions
        )
        
        return merged_position

    def _calculate_duration(self, start_time: datetime, end_time: datetime) -> float:
        """计算持仓时长（分钟）"""
        if start_time and end_time:
            return abs((end_time - start_time).total_seconds()) / 60
        return 0.0

    def _update_waiting_table(self, new_incomplete: Dict, completed_from_waiting: Dict):
        """
        更新等待表：添加新的不完整订单，移除已补全的订单
        """
        
        # 1. 移除已补全的订单
        if completed_from_waiting:
            completed_ids = list(completed_from_waiting.keys())
            self._remove_from_waiting_table(completed_ids)
            logger.info(f"从等待表移除已完成真实数据补齐的订单: {len(completed_ids)} 个")
        
        # 2. 添加新的不完整订单
        if new_incomplete:
            self._add_to_waiting_table(new_incomplete)
            logger.info(f"添加新的不完整订单到等待表: {len(new_incomplete)} 个")
        
        # 3. 更新检查时间和计数
        self._update_check_status()

    def _remove_from_waiting_table(self, position_ids: List[str]):
        """从等待表移除订单"""
        try:
            if not position_ids:
                return
                
            placeholders = ','.join(['?' for _ in position_ids])
            sql = f"DELETE FROM incomplete_positions_waiting WHERE position_id IN ({placeholders})"
            
            self.db_manager.execute_sql(sql, position_ids)
            logger.debug(f"成功移除 {len(position_ids)} 个订单")
            
        except Exception as e:
            logger.error(f"移除等待订单失败: {e}")

    def _add_to_waiting_table(self, incomplete_positions: Dict):
        """
        添加不完整订单到等待表

        🚀 修复：处理不同类型的不完整订单
        1. 只有开仓数据：正常添加到等待表
        2. 只有平仓数据：跳过（等待表设计不支持此类型）
        3. 部分平仓数据：正常添加到等待表
        """

        insert_data = []
        skipped_count = 0

        for pos_id, position in incomplete_positions.items():
            # 🚀 关键修复：检查是否有有效的开仓数据
            if position.first_open_time is None or position.total_open_amount <= 0:
                # 只有平仓数据的订单，等待表无法存储（因为first_open_time为NOT NULL）
                logger.debug(f"跳过订单 {pos_id}: 缺少开仓数据，无法存储到等待表")
                skipped_count += 1
                continue

            # 确保所有数值都转换为Python原生类型，避免numpy类型转换错误
            insert_data.append([
                str(pos_id),
                str(position.member_id),
                str(position.contract_name),
                int(position.primary_side) if hasattr(position.primary_side, 'item') else int(position.primary_side),
                position.first_open_time,  # 已确保不为None
                float(position.total_open_amount) if hasattr(position.total_open_amount, 'item') else float(position.total_open_amount),
                int(position.open_trades_count) if hasattr(position.open_trades_count, 'item') else int(position.open_trades_count),
                float(position.avg_open_price) if hasattr(position.avg_open_price, 'item') else float(position.avg_open_price),
                float(position.total_open_volume) if hasattr(position.total_open_volume, 'item') else float(position.total_open_volume),
                datetime.now(),  # waiting_since
                datetime.now(),  # last_check_time
                0,  # check_count
                str(self.task_id),  # source_task_id
                '1.0',  # data_version
                datetime.now(),  # created_at
                datetime.now()   # updated_at
            ])

        if skipped_count > 0:
            logger.info(f"⚠️  跳过 {skipped_count} 个只有平仓数据的订单（等待表不支持此类型）")
        
        # 批量插入（使用INSERT OR REPLACE避免重复）
        sql = """
        INSERT OR REPLACE INTO incomplete_positions_waiting 
        (position_id, member_id, contract_name, primary_side, first_open_time, 
         total_open_amount, open_trades_count, avg_open_price, total_open_volume,
         waiting_since, last_check_time, check_count, source_task_id, data_version,
         created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        try:
            self.db_manager.execute_many(sql, insert_data)
            logger.debug(f"成功添加 {len(insert_data)} 个不完整订单")
        except Exception as e:
            logger.error(f"添加等待订单失败: {e}")

    def _update_check_status(self):
        """更新检查状态"""
        try:
            sql = """
            UPDATE incomplete_positions_waiting 
            SET last_check_time = ?, check_count = check_count + 1
            WHERE last_check_time < ?
            """
            
            now = datetime.now()
            one_hour_ago = now - timedelta(hours=1)
            
            self.db_manager.execute_sql(sql, [now, one_hour_ago])
            
        except Exception as e:
            logger.error(f"更新检查状态失败: {e}")

    def _update_stats(self, total_processed: int, completed_from_waiting: int,
                     new_incomplete: int, processing_time: float, error_count: int = 0):
        """更新性能统计"""
        self.stats.update({
            'total_processed': total_processed,
            'completed_from_waiting': completed_from_waiting,
            'new_incomplete': new_incomplete,
            'processing_time': processing_time,
            'error_count': error_count,
            'success_rate': (total_processed - error_count) / total_processed * 100 if total_processed > 0 else 100
        })

    def get_processing_summary(self) -> Dict[str, Any]:
        """获取处理摘要信息"""
        return {
            'task_id': self.task_id,
            'stats': self.stats,
            'error_summary': self.error_handler.get_error_summary(),
            'timestamp': datetime.now().isoformat()
        }

    def get_stats(self) -> Dict[str, Any]:
        """获取处理统计信息"""
        return self.stats.copy()

    def cleanup_old_waiting_positions(self, max_waiting_days: int = 30, max_check_count: int = 100):
        """
        清理长期未匹配的等待订单
        """
        
        cutoff_date = datetime.now() - timedelta(days=max_waiting_days)
        
        # 删除超时或检查次数过多的记录
        cleanup_sql = """
        DELETE FROM incomplete_positions_waiting 
        WHERE waiting_since < ? OR check_count > ?
        """
        
        try:
            self.db_manager.execute_sql(cleanup_sql, [cutoff_date, max_check_count])
            logger.info(f"清理等待表，删除长期未匹配记录")
        except Exception as e:
            error = IncrementalProcessingError(
                ProcessingErrorType.DATABASE_OPERATION_ERROR,
                "清理等待表失败",
                {"cutoff_date": cutoff_date, "max_check_count": max_check_count},
                e
            )
            self.error_handler.handle_error(error, "等待表清理")

    def get_waiting_table_stats(self) -> Dict[str, Any]:
        """
        获取等待表统计信息
        """
        try:
            stats_sql = """
            SELECT
                COUNT(*) as total_waiting,
                COUNT(DISTINCT member_id) as unique_users,
                COUNT(DISTINCT contract_name) as unique_contracts,
                AVG(check_count) as avg_check_count,
                MIN(waiting_since) as oldest_waiting,
                MAX(waiting_since) as newest_waiting,
                SUM(total_open_amount) as total_amount_waiting
            FROM incomplete_positions_waiting
            """

            result = self.db_manager.execute_sql(stats_sql)
            if result:
                stats = result[0]
                return {
                    'total_waiting_positions': stats.get('total_waiting', 0),
                    'unique_users': stats.get('unique_users', 0),
                    'unique_contracts': stats.get('unique_contracts', 0),
                    'avg_check_count': round(stats.get('avg_check_count', 0), 2),
                    'oldest_waiting': stats.get('oldest_waiting'),
                    'newest_waiting': stats.get('newest_waiting'),
                    'total_amount_waiting': stats.get('total_amount_waiting', 0),
                    'timestamp': datetime.now().isoformat()
                }
            return {}

        except Exception as e:
            logger.error(f"获取等待表统计失败: {e}")
            return {}

    def get_waiting_positions_by_user(self, member_id: str) -> List[Dict]:
        """
        获取指定用户的等待订单
        """
        try:
            sql = """
            SELECT position_id, member_id, contract_name, primary_side,
                   first_open_time, total_open_amount, open_trades_count,
                   avg_open_price, total_open_volume, waiting_since,
                   last_check_time, check_count
            FROM incomplete_positions_waiting
            WHERE member_id = ?
            ORDER BY waiting_since ASC
            """

            results = self.db_manager.execute_sql(sql, [member_id])
            logger.debug(f"用户 {member_id} 的等待订单: {len(results)} 个")
            return results

        except Exception as e:
            logger.error(f"获取用户等待订单失败: {e}")
            return []

    def get_waiting_positions_by_contract(self, contract_name: str) -> List[Dict]:
        """
        获取指定合约的等待订单
        """
        try:
            sql = """
            SELECT position_id, member_id, contract_name, primary_side,
                   first_open_time, total_open_amount, open_trades_count,
                   avg_open_price, total_open_volume, waiting_since,
                   last_check_time, check_count
            FROM incomplete_positions_waiting
            WHERE contract_name = ?
            ORDER BY waiting_since ASC
            """

            results = self.db_manager.execute_sql(sql, [contract_name])
            logger.debug(f"合约 {contract_name} 的等待订单: {len(results)} 个")
            return results

        except Exception as e:
            logger.error(f"获取合约等待订单失败: {e}")
            return []

    def get_long_waiting_positions(self, hours_threshold: int = 24) -> List[Dict]:
        """
        获取长时间等待的订单
        """
        try:
            cutoff_time = datetime.now() - timedelta(hours=hours_threshold)

            sql = """
            SELECT position_id, member_id, contract_name, primary_side,
                   first_open_time, total_open_amount, waiting_since,
                   last_check_time, check_count
            FROM incomplete_positions_waiting
            WHERE waiting_since < ?
            ORDER BY waiting_since ASC
            """

            results = self.db_manager.execute_sql(sql, [cutoff_time])

            # 手动计算等待时间
            now = datetime.now()
            for result in results:
                waiting_since = result.get('waiting_since')
                if waiting_since:
                    if isinstance(waiting_since, str):
                        waiting_since = datetime.fromisoformat(waiting_since.replace('Z', '+00:00'))
                    waiting_hours = (now - waiting_since).total_seconds() / 3600
                    result['waiting_hours'] = waiting_hours
                else:
                    result['waiting_hours'] = 0

            logger.debug(f"长时间等待订单 (>{hours_threshold}小时): {len(results)} 个")
            return results

        except Exception as e:
            logger.error(f"获取长时间等待订单失败: {e}")
            return []

    def remove_specific_waiting_position(self, position_id: str) -> bool:
        """
        移除指定的等待订单
        """
        try:
            sql = "DELETE FROM incomplete_positions_waiting WHERE position_id = ?"
            self.db_manager.execute_sql(sql, [position_id])
            logger.info(f"成功移除等待订单: {position_id}")
            return True

        except Exception as e:
            logger.error(f"移除等待订单失败: {e}")
            return False

    def update_waiting_position_check_count(self, position_id: str) -> bool:
        """
        更新指定订单的检查计数
        """
        try:
            # 使用DELETE + INSERT的方式来避免UPDATE的主键冲突问题
            # 这可能是DuckDB的一个特殊行为

            # 1. 先查询现有记录
            query_sql = "SELECT * FROM incomplete_positions_waiting WHERE position_id = ?"
            result = self.db_manager.execute_sql(query_sql, [position_id])

            if not result:
                logger.warning(f"订单不存在: {position_id}")
                return False

            record = result[0]
            current_count = record.get('check_count', 0)
            new_count = current_count + 1

            # 2. 删除现有记录
            delete_sql = "DELETE FROM incomplete_positions_waiting WHERE position_id = ?"
            self.db_manager.execute_sql(delete_sql, [position_id])

            # 3. 插入更新后的记录
            insert_sql = """
            INSERT INTO incomplete_positions_waiting
            (position_id, member_id, contract_name, primary_side, first_open_time,
             total_open_amount, open_trades_count, avg_open_price, total_open_volume,
             waiting_since, last_check_time, check_count, source_task_id, data_version,
             created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            insert_data = [
                record['position_id'],
                record['member_id'],
                record['contract_name'],
                record['primary_side'],
                record['first_open_time'],
                record['total_open_amount'],
                record['open_trades_count'],
                record['avg_open_price'],
                record['total_open_volume'],
                record['waiting_since'],
                datetime.now(),  # 更新last_check_time
                new_count,       # 更新check_count
                record['source_task_id'],
                record['data_version'],
                record['created_at'],
                datetime.now()   # 更新updated_at
            ]

            self.db_manager.execute_sql(insert_sql, insert_data)
            logger.debug(f"更新订单检查计数: {position_id} ({current_count} -> {new_count})")
            return True

        except Exception as e:
            logger.error(f"更新检查计数失败: {e}")
            return False

    def get_performance_metrics(self) -> Dict[str, Any]:
        """
        获取详细的性能指标
        """
        try:
            # 基础统计
            basic_stats = self.get_stats()

            # 等待表统计
            waiting_stats = self.get_waiting_table_stats()

            # 计算性能指标
            processing_rate = 0
            if basic_stats.get('processing_time', 0) > 0:
                processing_rate = basic_stats.get('total_processed', 0) / basic_stats.get('processing_time', 1)

            completion_rate = 0
            if basic_stats.get('total_processed', 0) > 0:
                completion_rate = basic_stats.get('completed_from_waiting', 0) / basic_stats.get('total_processed', 1)

            error_rate = 0
            if basic_stats.get('total_processed', 0) > 0:
                error_rate = basic_stats.get('error_count', 0) / basic_stats.get('total_processed', 1)

            # 内存使用情况（简化版）
            import psutil
            process = psutil.Process()
            memory_info = process.memory_info()

            return {
                'task_id': self.task_id,
                'timestamp': datetime.now().isoformat(),

                # 处理性能
                'processing_performance': {
                    'total_processed': basic_stats.get('total_processed', 0),
                    'processing_time_seconds': basic_stats.get('processing_time', 0),
                    'processing_rate_per_second': round(processing_rate, 2),
                    'completion_rate': round(completion_rate * 100, 2),  # 百分比
                    'error_rate': round(error_rate * 100, 2),  # 百分比
                },

                # 等待表状态
                'waiting_table_status': {
                    'total_waiting': waiting_stats.get('total_waiting_positions', 0),
                    'unique_users': waiting_stats.get('unique_users', 0),
                    'unique_contracts': waiting_stats.get('unique_contracts', 0),
                    'avg_check_count': waiting_stats.get('avg_check_count', 0),
                    'total_amount_waiting': waiting_stats.get('total_amount_waiting', 0),
                },

                # 系统资源
                'system_resources': {
                    'memory_usage_mb': round(memory_info.rss / 1024 / 1024, 2),
                    'memory_usage_percent': round(process.memory_percent(), 2),
                    'cpu_percent': round(process.cpu_percent(), 2),
                },

                # 错误统计
                'error_summary': self.error_handler.get_error_summary(),
            }

        except Exception as e:
            logger.error(f"获取性能指标失败: {e}")
            return {
                'task_id': self.task_id,
                'timestamp': datetime.now().isoformat(),
                'error': str(e)
            }

    def check_performance_alerts(self, thresholds: Dict[str, float] = None) -> List[Dict[str, Any]]:
        """
        检查性能告警
        """
        if thresholds is None:
            thresholds = {
                'max_processing_time': 300.0,  # 最大处理时间（秒）
                'max_error_rate': 5.0,         # 最大错误率（%）
                'max_waiting_positions': 1000, # 最大等待订单数
                'max_memory_usage': 80.0,      # 最大内存使用率（%）
                'min_completion_rate': 50.0,   # 最小完成率（%）
            }

        alerts = []

        try:
            metrics = self.get_performance_metrics()

            # 检查处理时间
            processing_time = metrics.get('processing_performance', {}).get('processing_time_seconds', 0)
            if processing_time > thresholds['max_processing_time']:
                alerts.append({
                    'type': 'PERFORMANCE_WARNING',
                    'level': 'WARNING',
                    'message': f'处理时间过长: {processing_time:.2f}秒 > {thresholds["max_processing_time"]}秒',
                    'metric': 'processing_time',
                    'value': processing_time,
                    'threshold': thresholds['max_processing_time'],
                    'timestamp': datetime.now().isoformat()
                })

            # 检查错误率
            error_rate = metrics.get('processing_performance', {}).get('error_rate', 0)
            if error_rate > thresholds['max_error_rate']:
                alerts.append({
                    'type': 'ERROR_RATE_HIGH',
                    'level': 'ERROR',
                    'message': f'错误率过高: {error_rate:.2f}% > {thresholds["max_error_rate"]}%',
                    'metric': 'error_rate',
                    'value': error_rate,
                    'threshold': thresholds['max_error_rate'],
                    'timestamp': datetime.now().isoformat()
                })

            # 检查等待订单数
            waiting_count = metrics.get('waiting_table_status', {}).get('total_waiting', 0)
            if waiting_count > thresholds['max_waiting_positions']:
                alerts.append({
                    'type': 'WAITING_QUEUE_FULL',
                    'level': 'WARNING',
                    'message': f'等待订单数过多: {waiting_count} > {thresholds["max_waiting_positions"]}',
                    'metric': 'waiting_positions',
                    'value': waiting_count,
                    'threshold': thresholds['max_waiting_positions'],
                    'timestamp': datetime.now().isoformat()
                })

            # 检查内存使用率
            memory_percent = metrics.get('system_resources', {}).get('memory_usage_percent', 0)
            if memory_percent > thresholds['max_memory_usage']:
                alerts.append({
                    'type': 'MEMORY_USAGE_HIGH',
                    'level': 'WARNING',
                    'message': f'内存使用率过高: {memory_percent:.2f}% > {thresholds["max_memory_usage"]}%',
                    'metric': 'memory_usage',
                    'value': memory_percent,
                    'threshold': thresholds['max_memory_usage'],
                    'timestamp': datetime.now().isoformat()
                })

            # 检查完成率
            completion_rate = metrics.get('processing_performance', {}).get('completion_rate', 0)
            if completion_rate < thresholds['min_completion_rate'] and completion_rate > 0:
                alerts.append({
                    'type': 'COMPLETION_RATE_LOW',
                    'level': 'WARNING',
                    'message': f'完成率过低: {completion_rate:.2f}% < {thresholds["min_completion_rate"]}%',
                    'metric': 'completion_rate',
                    'value': completion_rate,
                    'threshold': thresholds['min_completion_rate'],
                    'timestamp': datetime.now().isoformat()
                })

            if alerts:
                logger.warning(f"检测到 {len(alerts)} 个性能告警")
                for alert in alerts:
                    logger.warning(f"  {alert['level']}: {alert['message']}")
            else:
                logger.debug("性能检查正常，无告警")

            return alerts

        except Exception as e:
            logger.error(f"性能告警检查失败: {e}")
            return [{
                'type': 'ALERT_CHECK_ERROR',
                'level': 'ERROR',
                'message': f'性能告警检查失败: {e}',
                'timestamp': datetime.now().isoformat()
            }]

    def log_performance_summary(self):
        """
        记录性能摘要日志
        """
        try:
            metrics = self.get_performance_metrics()
            alerts = self.check_performance_alerts()

            logger.info("=" * 60)
            logger.info("性能监控摘要")
            logger.info("=" * 60)

            # 处理性能
            perf = metrics.get('processing_performance', {})
            logger.info(f"处理性能:")
            logger.info(f"  总处理数: {perf.get('total_processed', 0)}")
            logger.info(f"  处理时间: {perf.get('processing_time_seconds', 0):.2f}秒")
            logger.info(f"  处理速率: {perf.get('processing_rate_per_second', 0):.2f}/秒")
            logger.info(f"  完成率: {perf.get('completion_rate', 0):.2f}%")
            logger.info(f"  错误率: {perf.get('error_rate', 0):.2f}%")

            # 等待表状态
            waiting = metrics.get('waiting_table_status', {})
            logger.info(f"等待表状态:")
            logger.info(f"  等待订单数: {waiting.get('total_waiting', 0)}")
            logger.info(f"  涉及用户数: {waiting.get('unique_users', 0)}")
            logger.info(f"  涉及合约数: {waiting.get('unique_contracts', 0)}")
            logger.info(f"  平均检查次数: {waiting.get('avg_check_count', 0):.2f}")

            # 系统资源
            resources = metrics.get('system_resources', {})
            logger.info(f"系统资源:")
            logger.info(f"  内存使用: {resources.get('memory_usage_mb', 0):.2f}MB ({resources.get('memory_usage_percent', 0):.2f}%)")
            logger.info(f"  CPU使用: {resources.get('cpu_percent', 0):.2f}%")

            # 告警信息
            if alerts:
                logger.info(f"告警信息: {len(alerts)} 个")
                for alert in alerts:
                    logger.info(f"  {alert['level']}: {alert['message']}")
            else:
                logger.info("告警信息: 无")

            logger.info("=" * 60)

        except Exception as e:
            logger.error(f"记录性能摘要失败: {e}")

    def check_duplicate_data(self, new_positions: Dict[str, CompletePosition]) -> Dict[str, Any]:
        """
        检查新数据中的重复情况
        """
        try:
            # 检查等待表中已存在的position_id
            existing_positions = set()
            if new_positions:
                position_ids = list(new_positions.keys())
                placeholders = ','.join(['?' for _ in position_ids])
                check_sql = f"""
                SELECT position_id FROM incomplete_positions_waiting
                WHERE position_id IN ({placeholders})
                """

                results = self.db_manager.execute_sql(check_sql, position_ids)
                existing_positions = set(result['position_id'] for result in results)

            # 分析重复情况
            duplicate_analysis = {
                'total_new_positions': len(new_positions),
                'existing_in_waiting': len(existing_positions),
                'truly_new_positions': len(new_positions) - len(existing_positions),
                'duplicate_position_ids': list(existing_positions),
                'duplicate_rate': len(existing_positions) / len(new_positions) * 100 if new_positions else 0,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"重复数据分析: 新数据{len(new_positions)}个，重复{len(existing_positions)}个，"
                       f"重复率{duplicate_analysis['duplicate_rate']:.1f}%")

            return duplicate_analysis

        except Exception as e:
            logger.error(f"重复数据检查失败: {e}")
            return {
                'total_new_positions': len(new_positions),
                'existing_in_waiting': 0,
                'truly_new_positions': len(new_positions),
                'duplicate_position_ids': [],
                'duplicate_rate': 0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def handle_duplicate_positions(self, new_positions: Dict[str, CompletePosition],
                                 strategy: str = 'smart_update') -> Dict[str, Any]:
        """
        智能处理重复数据

        策略选项:
        - 'skip': 跳过所有重复数据
        - 'replace': 替换所有重复数据
        - 'smart_update': 智能更新（比较数据版本和完整性）
        - 'merge': 合并数据（保留最佳信息）
        """
        try:
            duplicate_info = self.check_duplicate_data(new_positions)
            duplicate_ids = set(duplicate_info['duplicate_position_ids'])

            if not duplicate_ids:
                logger.info("无重复数据，直接处理")
                return {
                    'strategy': strategy,
                    'processed_count': len(new_positions),
                    'skipped_count': 0,
                    'updated_count': 0,
                    'actions': []
                }

            actions = []
            processed_count = 0
            skipped_count = 0
            updated_count = 0

            for position_id, position in new_positions.items():
                if position_id not in duplicate_ids:
                    # 非重复数据，正常处理
                    processed_count += 1
                    actions.append({
                        'position_id': position_id,
                        'action': 'process_new',
                        'reason': '新数据'
                    })
                    continue

                # 处理重复数据
                if strategy == 'skip':
                    skipped_count += 1
                    actions.append({
                        'position_id': position_id,
                        'action': 'skip',
                        'reason': '跳过重复数据'
                    })

                elif strategy == 'replace':
                    updated_count += 1
                    self._replace_waiting_position(position_id, position)
                    actions.append({
                        'position_id': position_id,
                        'action': 'replace',
                        'reason': '强制替换重复数据'
                    })

                elif strategy == 'smart_update':
                    action = self._smart_update_waiting_position(position_id, position)
                    if action == 'updated':
                        updated_count += 1
                    elif action == 'skipped':
                        skipped_count += 1
                    else:
                        processed_count += 1

                    actions.append({
                        'position_id': position_id,
                        'action': action,
                        'reason': '智能更新决策'
                    })

                elif strategy == 'merge':
                    action = self._merge_waiting_position(position_id, position)
                    if action == 'merged':
                        updated_count += 1
                    else:
                        skipped_count += 1

                    actions.append({
                        'position_id': position_id,
                        'action': action,
                        'reason': '数据合并'
                    })

            result = {
                'strategy': strategy,
                'processed_count': processed_count,
                'skipped_count': skipped_count,
                'updated_count': updated_count,
                'total_duplicates': len(duplicate_ids),
                'duplicate_rate': duplicate_info['duplicate_rate'],
                'actions': actions,
                'timestamp': datetime.now().isoformat()
            }

            logger.info(f"重复数据处理完成 - 策略: {strategy}, "
                       f"处理: {processed_count}, 跳过: {skipped_count}, 更新: {updated_count}")

            return result

        except Exception as e:
            logger.error(f"重复数据处理失败: {e}")
            return {
                'strategy': strategy,
                'processed_count': 0,
                'skipped_count': 0,
                'updated_count': 0,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }

    def _replace_waiting_position(self, position_id: str, new_position: CompletePosition):
        """替换等待表中的订单"""
        try:
            # 删除旧记录
            delete_sql = "DELETE FROM incomplete_positions_waiting WHERE position_id = ?"
            self.db_manager.execute_sql(delete_sql, [position_id])

            # 插入新记录
            self._add_single_position_to_waiting(position_id, new_position)

            logger.debug(f"替换等待订单: {position_id}")

        except Exception as e:
            logger.error(f"替换等待订单失败 {position_id}: {e}")

    def _smart_update_waiting_position(self, position_id: str, new_position: CompletePosition) -> str:
        """
        智能更新等待表中的订单
        返回: 'updated', 'skipped', 'processed'
        """
        try:
            # 获取现有记录
            query_sql = """
            SELECT * FROM incomplete_positions_waiting
            WHERE position_id = ?
            """
            results = self.db_manager.execute_sql(query_sql, [position_id])

            if not results:
                # 记录不存在，作为新数据处理
                return 'processed'

            existing = results[0]

            # 比较数据质量和完整性
            should_update = False
            update_reasons = []

            # 1. 检查数据完整性（新数据是否更完整）
            if new_position.is_completed and not existing.get('is_completed', False):
                should_update = True
                update_reasons.append('新数据更完整')

            # 2. 检查数据新鲜度（基于时间戳）
            existing_time = existing.get('updated_at')
            if existing_time:
                if isinstance(existing_time, str):
                    existing_time = datetime.fromisoformat(existing_time.replace('Z', '+00:00'))

                # 如果新数据比现有数据新超过1小时，考虑更新
                time_diff = datetime.now() - existing_time
                if time_diff.total_seconds() > 3600:  # 1小时
                    should_update = True
                    update_reasons.append('数据较旧需要更新')

            # 3. 检查数量精度（新数据是否有更精确的数量信息）
            existing_amount = existing.get('total_open_amount', 0)
            new_amount = new_position.total_open_amount

            if abs(new_amount - existing_amount) > 0.01:  # 数量有显著差异
                should_update = True
                update_reasons.append('数量信息更新')

            # 4. 检查检查次数（避免过度更新）
            check_count = existing.get('check_count', 0)
            if check_count > 10:  # 已经检查过很多次
                should_update = False
                update_reasons.append('检查次数过多，跳过更新')

            if should_update and update_reasons:
                self._replace_waiting_position(position_id, new_position)
                logger.debug(f"智能更新订单 {position_id}: {', '.join(update_reasons)}")
                return 'updated'
            else:
                # 只更新检查计数
                self.update_waiting_position_check_count(position_id)
                logger.debug(f"跳过更新订单 {position_id}: 无需更新")
                return 'skipped'

        except Exception as e:
            logger.error(f"智能更新失败 {position_id}: {e}")
            return 'skipped'

    def _merge_waiting_position(self, position_id: str, new_position: CompletePosition) -> str:
        """
        合并等待表中的订单数据
        返回: 'merged', 'skipped'
        """
        try:
            # 获取现有记录
            query_sql = """
            SELECT * FROM incomplete_positions_waiting
            WHERE position_id = ?
            """
            results = self.db_manager.execute_sql(query_sql, [position_id])

            if not results:
                return 'skipped'

            existing = results[0]

            # 合并逻辑：保留最佳信息
            merged_data = {
                'position_id': position_id,
                'member_id': existing.get('member_id') or new_position.member_id,
                'contract_name': existing.get('contract_name') or new_position.contract_name,
                'primary_side': existing.get('primary_side') or new_position.primary_side,

                # 时间信息：保留最早的开仓时间
                'first_open_time': min(
                    existing.get('first_open_time', new_position.first_open_time),
                    new_position.first_open_time
                ),

                # 数量信息：保留更大的数量（可能更完整）
                'total_open_amount': max(
                    existing.get('total_open_amount', 0),
                    new_position.total_open_amount
                ),

                # 交易次数：保留更大的值
                'open_trades_count': max(
                    existing.get('open_trades_count', 0),
                    new_position.open_trades_count
                ),

                # 价格信息：使用加权平均
                'avg_open_price': (
                    existing.get('avg_open_price', 0) + new_position.avg_open_price
                ) / 2,

                'total_open_volume': max(
                    existing.get('total_open_volume', 0),
                    new_position.total_open_volume
                ),
            }

            # 创建合并后的CompletePosition对象
            merged_position = CompletePosition(
                position_id=merged_data['position_id'],
                member_id=merged_data['member_id'],
                contract_name=merged_data['contract_name'],
                primary_side=merged_data['primary_side'],
                first_open_time=merged_data['first_open_time'],
                last_open_time=merged_data['first_open_time'],
                total_open_amount=merged_data['total_open_amount'],
                total_open_volume=merged_data['total_open_volume'],
                avg_open_price=merged_data['avg_open_price'],
                open_trades_count=merged_data['open_trades_count'],

                # 其他字段使用默认值
                first_close_time=None,
                last_close_time=None,
                total_close_amount=0,
                total_close_volume=0,
                avg_close_price=0,
                close_trades_count=0,
                is_completed=False,
                total_duration_minutes=0,
                real_profit=0,
                calculated_profit=0,
                is_quick_trade=False,
                is_scalping=False,
                add_position_count=0,
                reduce_position_count=0,
                risk_score=0.0,
                abnormal_flags=[],
                leverage=new_position.leverage,
                total_fee=0,
                market_orders_open=0,
                limit_orders_open=0,
                market_orders_close=0,
                limit_orders_close=0,
                cross_margin_positions=0,
                isolated_margin_positions=0
            )

            # 替换记录
            self._replace_waiting_position(position_id, merged_position)
            logger.debug(f"合并订单数据: {position_id}")
            return 'merged'

        except Exception as e:
            logger.error(f"合并订单数据失败 {position_id}: {e}")
            return 'skipped'

    def _add_single_position_to_waiting(self, position_id: str, position: CompletePosition):
        """添加单个订单到等待表"""
        try:
            insert_sql = """
            INSERT INTO incomplete_positions_waiting
            (position_id, member_id, contract_name, primary_side, first_open_time,
             total_open_amount, open_trades_count, avg_open_price, total_open_volume,
             waiting_since, last_check_time, check_count, source_task_id, data_version,
             created_at, updated_at)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """

            insert_data = [
                position.position_id,
                position.member_id,
                position.contract_name,
                position.primary_side,
                position.first_open_time,
                position.total_open_amount,
                position.open_trades_count,
                position.avg_open_price,
                position.total_open_volume,
                datetime.now(),  # waiting_since
                datetime.now(),  # last_check_time
                0,  # check_count
                self.task_id,  # source_task_id
                1,  # data_version
                datetime.now(),  # created_at
                datetime.now()   # updated_at
            ]

            self.db_manager.execute_sql(insert_sql, insert_data)

        except Exception as e:
            logger.error(f"添加单个订单到等待表失败 {position_id}: {e}")

    def get_duplicate_processing_stats(self) -> Dict[str, Any]:
        """获取重复数据处理统计"""
        try:
            # 统计等待表中的重复检查情况
            stats_sql = """
            SELECT
                COUNT(*) as total_positions,
                COUNT(CASE WHEN check_count > 1 THEN 1 END) as rechecked_positions,
                AVG(check_count) as avg_check_count,
                MAX(check_count) as max_check_count,
                COUNT(CASE WHEN check_count > 5 THEN 1 END) as high_check_positions,
                COUNT(CASE WHEN check_count > 10 THEN 1 END) as very_high_check_positions
            FROM incomplete_positions_waiting
            """

            result = self.db_manager.execute_sql(stats_sql)

            if result:
                stats = result[0]
                return {
                    'total_waiting_positions': stats.get('total_positions', 0),
                    'rechecked_positions': stats.get('rechecked_positions', 0),
                    'avg_check_count': round(stats.get('avg_check_count', 0), 2),
                    'max_check_count': stats.get('max_check_count', 0),
                    'high_check_positions': stats.get('high_check_positions', 0),
                    'very_high_check_positions': stats.get('very_high_check_positions', 0),
                    'recheck_rate': round(
                        stats.get('rechecked_positions', 0) / max(stats.get('total_positions', 1), 1) * 100, 2
                    ),
                    'timestamp': datetime.now().isoformat()
                }

            return {}

        except Exception as e:
            logger.error(f"获取重复处理统计失败: {e}")
            return {'error': str(e), 'timestamp': datetime.now().isoformat()}

    def _convert_positions_to_dataframe(self, complete_positions: Dict[str, CompletePosition]) -> pd.DataFrame:
        """
        将完整订单转换为DataFrame格式，供分析器使用

        参数:
            complete_positions: 完整订单字典

        返回:
            pd.DataFrame: 转换后的DataFrame
        """
        try:
            if not complete_positions:
                return pd.DataFrame()

            # 转换为记录列表
            records = []
            for position_id, position in complete_positions.items():
                record = {
                    'position_id': position_id,
                    'member_id': position.member_id,
                    'contract_name': position.contract_name,
                    'first_open_time': position.first_open_time,
                    'last_open_time': position.last_open_time,
                    'total_open_amount': position.total_open_amount,
                    'total_open_volume': position.total_open_volume,
                    'avg_open_price': position.avg_open_price,
                    'open_trades_count': position.open_trades_count,
                    'primary_side': position.primary_side,
                    'first_close_time': position.first_close_time,
                    'last_close_time': position.last_close_time,
                    'total_close_amount': position.total_close_amount,
                    'total_close_volume': position.total_close_volume,
                    'avg_close_price': position.avg_close_price,
                    'close_trades_count': position.close_trades_count,
                    'is_completed': position.is_completed,
                    'total_duration_minutes': position.total_duration_minutes,
                    'real_profit': position.real_profit,
                    'calculated_profit': position.calculated_profit,
                    'is_quick_trade': position.is_quick_trade,
                    'is_scalping': position.is_scalping,
                    'add_position_count': position.add_position_count,
                    'reduce_position_count': position.reduce_position_count,
                    'risk_score': position.risk_score,
                    'abnormal_flags': position.abnormal_flags,
                    'leverage': position.leverage,
                    'total_fee': position.total_fee,
                    'market_orders_open': position.market_orders_open,
                    'limit_orders_open': position.limit_orders_open,
                    'market_orders_close': position.market_orders_close,
                    'limit_orders_close': position.limit_orders_close,
                    'cross_margin_positions': position.cross_margin_positions,
                    'isolated_margin_positions': position.isolated_margin_positions
                }
                records.append(record)

            df = pd.DataFrame(records)
            logger.info(f"成功转换 {len(records)} 个完整订单为DataFrame")
            return df

        except Exception as e:
            logger.error(f"转换完整订单为DataFrame失败: {str(e)}")
            return pd.DataFrame()

    def _merge_waiting_and_complete_data(self, waiting_data: pd.DataFrame, complete_data: pd.DataFrame) -> pd.DataFrame:
        """合并等待表数据和完整数据"""
        if waiting_data.empty:
            return complete_data
        if complete_data.empty:
            return waiting_data

        # 基于position_id合并数据
        merged_data = []

        for _, complete_row in complete_data.iterrows():
            pos_id = complete_row['position_id']

            # 查找等待表中的对应记录
            waiting_match = waiting_data[waiting_data['position_id'] == pos_id]

            if not waiting_match.empty:
                # 合并数据：使用完整数据的数量，保留等待表的历史时间
                waiting_row = waiting_match.iloc[0]
                merged_row = complete_row.copy()
                merged_row['created_at'] = waiting_row['created_at']  # 保留历史开仓时间
                merged_row['check_count'] = waiting_row.get('check_count', 0)
                merged_data.append(merged_row)
            else:
                # 没有等待表记录，直接使用完整数据
                merged_data.append(complete_row)

        return pd.DataFrame(merged_data)

    def _should_smart_update(self, existing_data: dict, new_data: dict) -> bool:
        """智能更新判断逻辑"""
        config = self.config.duplicate_handling.smart_update

        # 计算数据质量分数
        existing_score = (
            existing_data.get('completeness_score', 0) * config.completeness_weight +
            existing_data.get('data_freshness', 0) * config.freshness_weight +
            (1.0 / max(existing_data.get('check_count', 1), 1)) * config.check_count_weight
        )

        new_score = (
            new_data.get('completeness_score', 0) * config.completeness_weight +
            new_data.get('data_freshness', 0) * config.freshness_weight +
            (1.0 / max(new_data.get('check_count', 1), 1)) * config.check_count_weight
        )

        # 如果新数据质量分数超过阈值，则更新
        return new_score > existing_score and new_score >= config.update_threshold

    def _get_duplicate_action(self, position_id: str, existing_data: dict, new_data: dict) -> str:
        """获取重复数据处理动作"""
        strategy = self.config.duplicate_handling.default_strategy

        if strategy == 'skip':
            return 'skip'
        elif strategy == 'replace':
            return 'replace'
        elif strategy == 'smart_update':
            return 'update' if self._should_smart_update(existing_data, new_data) else 'skip'
        elif strategy == 'merge':
            return 'merge'
        else:
            return 'skip'

    def _check_has_open_trades(self, df: pd.DataFrame) -> bool:
        """
        检查数据中是否包含开仓记录

        Args:
            df: 交易数据DataFrame

        Returns:
            bool: True表示有开仓记录，False表示只有平仓记录
        """
        if 'side' not in df.columns:
            logger.warning("数据中缺少side字段，假设包含开仓记录")
            return True

        # side: 1=开多, 2=平空, 3=开空, 4=平多
        open_sides = {1, 3}  # 开仓操作
        close_sides = {2, 4}  # 平仓操作

        unique_sides = set(df['side'].unique())
        has_open = bool(unique_sides & open_sides)
        has_close = bool(unique_sides & close_sides)

        logger.info(f"🔍 数据类型检查: 开仓记录={has_open}, 平仓记录={has_close}, sides={unique_sides}")
        return has_open

    def _create_raw_position_fragments(self, df: pd.DataFrame) -> Dict[str, CompletePosition]:
        """
        从原始数据创建订单片段，基于真实交易记录，不进行任何推算

        Args:
            df: 原始交易数据DataFrame

        Returns:
            Dict[str, CompletePosition]: 订单片段字典（都标记为不完整，等待与历史数据补齐）
        """
        logger.info(f"🚀 基于真实交易记录创建订单片段，数据量: {len(df)}")

        fragments = {}

        # 按position_id分组
        for position_id, group in df.groupby('position_id'):
            group_sorted = group.sort_values('create_time')

            # 分离开仓和平仓记录
            open_trades = group_sorted[group_sorted['side'].isin([1, 3])]  # 1=开多, 3=开空
            close_trades = group_sorted[group_sorted['side'].isin([2, 4])]  # 2=平空, 4=平多

            # 创建订单片段，只记录实际存在的数据
            fragment = CompletePosition(
                position_id=str(position_id),
                member_id=str(group_sorted.iloc[0]['member_id']),
                contract_name=str(group_sorted.iloc[0]['contract_name']),

                # 开仓信息：只记录实际存在的开仓数据
                first_open_time=open_trades.iloc[0]['create_time'] if len(open_trades) > 0 else None,
                last_open_time=open_trades.iloc[-1]['create_time'] if len(open_trades) > 0 else None,
                total_open_amount=open_trades['deal_vol_usdt'].sum() if len(open_trades) > 0 else 0.0,
                total_open_volume=open_trades.get('deal_vol', open_trades['deal_vol_usdt']).sum() if len(open_trades) > 0 else 0.0,
                avg_open_price=self._calculate_weighted_avg_price(open_trades) if len(open_trades) > 0 else 0.0,
                open_trades_count=len(open_trades),
                primary_side=self._get_primary_side_from_trades(group_sorted),

                # 平仓信息：只记录实际存在的平仓数据
                first_close_time=close_trades.iloc[0]['create_time'] if len(close_trades) > 0 else None,
                last_close_time=close_trades.iloc[-1]['create_time'] if len(close_trades) > 0 else None,
                total_close_amount=close_trades['deal_vol_usdt'].sum() if len(close_trades) > 0 else 0.0,
                total_close_volume=close_trades.get('deal_vol', close_trades['deal_vol_usdt']).sum() if len(close_trades) > 0 else 0.0,
                avg_close_price=self._calculate_weighted_avg_price(close_trades) if len(close_trades) > 0 else 0.0,
                close_trades_count=len(close_trades),

                # 🚀 修复：增量模式下进行初步完整性判断
                # 如果有开仓和平仓数据且数量匹配，可以标记为完整
                is_completed=self._check_fragment_completeness(open_trades, close_trades),

                # 其他字段
                total_duration_minutes=self._calculate_position_duration(open_trades, close_trades),
                real_profit=group_sorted.get('profit', pd.Series([0.0])).sum(),
                calculated_profit=0.0,  # 不计算，等真实数据补齐后再算
                is_quick_trade=False,  # 等真实数据补齐后再判断
                is_scalping=False,     # 等真实数据补齐后再判断
                add_position_count=len(open_trades),
                reduce_position_count=len(close_trades),
                risk_score=0.0,        # 等真实数据补齐后再计算
                abnormal_flags=[],     # 等真实数据补齐后再分析
                leverage=1.0,          # 默认值
                total_fee=0.0          # 默认值
            )

            fragments[str(position_id)] = fragment
            logger.info(f"  创建片段: {position_id}, 开仓={len(open_trades)}, 平仓={len(close_trades)}")

        logger.info(f"✅ 真实交易片段创建完成，共 {len(fragments)} 个片段（等待与历史数据补齐）")
        return fragments

    def _check_fragment_completeness(self, open_trades: pd.DataFrame, close_trades: pd.DataFrame) -> bool:
        """
        检查数据片段的完整性

        增量模式的完整性判断逻辑：
        1. 必须同时有开仓和平仓数据
        2. 开仓和平仓数量必须匹配（在容差范围内）
        3. 如果完整，可以直接进入分析流程
        4. 如果不完整，进入等待表等待补全

        Args:
            open_trades: 开仓交易数据
            close_trades: 平仓交易数据

        Returns:
            bool: 是否完整
        """

        # 1. 基础检查：必须同时有开仓和平仓数据
        if len(open_trades) == 0 or len(close_trades) == 0:
            return False

        # 2. 数量匹配检查
        open_amount = open_trades['deal_vol_usdt'].sum()
        close_amount = close_trades['deal_vol_usdt'].sum()

        if open_amount <= 0 or close_amount <= 0:
            return False

        # 3. 使用与_is_position_truly_completed相同的容差逻辑
        vol_diff = abs(open_amount - close_amount)

        # 绝对容差
        absolute_tolerance = self.config.matching.tolerance.absolute
        if vol_diff <= absolute_tolerance:
            return True

        # 相对容差
        relative_tolerance = self.config.matching.tolerance.relative
        relative_diff = vol_diff / open_amount
        if relative_diff <= relative_tolerance:
            return True

        # 95%以上完成度
        min_completion_ratio = self.config.completion_check.min_completion_ratio
        if close_amount >= open_amount * min_completion_ratio:
            return True

        return False

    def _get_primary_side_from_trades(self, trades: pd.DataFrame) -> int:
        """从交易记录中获取主要方向"""
        sides = trades['side'].unique()

        # 优先从开仓记录判断
        if 1 in sides:  # 开多
            return 1
        elif 3 in sides:  # 开空
            return 3

        # 如果没有开仓记录，从平仓记录推断
        if 2 in sides:  # 平空 -> 原来是空头
            return 3
        elif 4 in sides:  # 平多 -> 原来是多头
            return 1

        return 1  # 默认多头

    def _calculate_position_duration(self, open_trades: pd.DataFrame, close_trades: pd.DataFrame) -> float:
        """计算持仓时长（分钟）"""
        if len(open_trades) > 0 and len(close_trades) > 0:
            first_open = open_trades.iloc[0]['create_time']
            last_close = close_trades.iloc[-1]['create_time']

            # 确保时间字段是datetime类型
            if isinstance(first_open, str):
                first_open = pd.to_datetime(first_open)
            if isinstance(last_close, str):
                last_close = pd.to_datetime(last_close)

            duration = (last_close - first_open).total_seconds() / 60.0
            return max(0.0, duration)
        return 0.0

    def _calculate_weighted_avg_price(self, trades: pd.DataFrame) -> float:
        """计算加权平均价格"""
        if 'deal_avg_price' in trades.columns and len(trades) > 0:
            weights = trades['deal_vol_usdt']
            if weights.sum() > 0:
                return (trades['deal_avg_price'] * weights).sum() / weights.sum()
        return 0.0
