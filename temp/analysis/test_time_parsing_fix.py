#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试增量处理器中时间类型错误修复
"""

import os
import sys
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, os.path.join(project_root, 'backend'))

from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition

def test_time_parsing():
    """测试时间解析功能"""
    print("🧪 测试时间解析功能...")
    
    # 创建处理器实例
    processor = SimpleIncrementalProcessor()
    
    # 测试不同格式的时间字符串
    test_times = [
        "2025-08-02T18:58:09.647",
        "2025-08-02T18:58:09.647Z",
        "2025-08-02 18:58:09",
        "2025-08-02T18:58:09+00:00",
        datetime.now(),  # 已经是datetime对象
        None  # 空值
    ]
    
    print("\n📅 测试 _calculate_duration 方法:")
    for i, start_time in enumerate(test_times):
        for j, end_time in enumerate(test_times):
            if start_time is None or end_time is None:
                continue
            try:
                duration = processor._calculate_duration(start_time, end_time)
                print(f"  ✅ 测试 {i}-{j}: {type(start_time).__name__} -> {type(end_time).__name__} = {duration:.2f}分钟")
            except Exception as e:
                print(f"  ❌ 测试 {i}-{j}: {type(start_time).__name__} -> {type(end_time).__name__} 失败: {e}")

def test_merge_waiting_data():
    """测试合并等待表数据功能"""
    print("\n🔄 测试合并等待表数据功能...")
    
    processor = SimpleIncrementalProcessor()
    
    # 模拟等待表数据（包含字符串时间）
    waiting_pos = {
        'position_id': '3861283',
        'member_id': '12345',
        'contract_name': 'BTCUSDT',
        'primary_side': 1,
        'first_open_time': '2025-08-02T18:58:09.647',  # 字符串格式
        'total_open_amount': 1000.0,
        'total_open_volume': 0.01,
        'avg_open_price': 100000.0,
        'open_trades_count': 1
    }
    
    # 模拟新数据（CompletePosition对象）
    new_pos = CompletePosition(
        position_id='3861283',
        member_id='12345',
        contract_name='BTCUSDT',
        first_open_time=None,
        last_open_time=None,
        total_open_amount=0.0,
        total_open_volume=0.0,
        avg_open_price=0.0,
        open_trades_count=0,
        primary_side=1,
        first_close_time=datetime.now(),
        last_close_time=datetime.now(),
        total_close_amount=1000.0,
        total_close_volume=0.01,
        avg_close_price=100000.0,
        close_trades_count=1,
        is_completed=False,
        total_duration_minutes=0.0,
        real_profit=0.0,
        calculated_profit=0.0,
        is_quick_trade=False,
        is_scalping=False,
        add_position_count=0,
        reduce_position_count=1,
        risk_score=0.0,
        abnormal_flags=[],
        leverage=10.0,
        total_fee=1.0,
        market_orders_open=0,
        limit_orders_open=0,
        market_orders_close=1,
        limit_orders_close=0,
        cross_margin_positions=1,
        isolated_margin_positions=0
    )
    
    try:
        # 测试合并功能
        merged_pos = processor._merge_waiting_and_new_data(waiting_pos, new_pos)
        print(f"  ✅ 合并成功: position_id={merged_pos.position_id}")
        print(f"  📅 开仓时间类型: {type(merged_pos.first_open_time)}")
        print(f"  ⏱️  持仓时长: {merged_pos.total_duration_minutes:.2f}分钟")
        
    except Exception as e:
        print(f"  ❌ 合并失败: {e}")
        import traceback
        traceback.print_exc()

def test_error_scenarios():
    """测试错误场景"""
    print("\n⚠️  测试错误场景...")
    
    processor = SimpleIncrementalProcessor()
    
    # 测试无效时间格式
    invalid_times = [
        "invalid-time-format",
        "2025-13-45T25:70:99",  # 无效日期时间
        "",  # 空字符串
        "null",  # 字符串null
    ]
    
    valid_time = datetime.now()
    
    for invalid_time in invalid_times:
        try:
            duration = processor._calculate_duration(invalid_time, valid_time)
            print(f"  ⚠️  意外成功: '{invalid_time}' -> {duration:.2f}分钟")
        except Exception as e:
            print(f"  ✅ 预期错误: '{invalid_time}' -> {type(e).__name__}: {e}")

if __name__ == "__main__":
    print("🚀 开始测试增量处理器时间解析修复...")
    
    try:
        test_time_parsing()
        test_merge_waiting_data()
        test_error_scenarios()
        
        print("\n✅ 所有测试完成！")
        
    except Exception as e:
        print(f"\n❌ 测试过程中发生错误: {e}")
        import traceback
        traceback.print_exc()
