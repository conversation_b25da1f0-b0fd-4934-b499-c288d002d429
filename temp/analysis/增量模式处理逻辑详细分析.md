# 增量模式：SimpleIncrementalProcessor 详细处理逻辑

## 🎯 核心思想
**基于等待表的历史数据补全** - 保持数据真实性，通过历史数据匹配进行补全

## 📋 处理流程

### 1. 数据验证阶段
```python
# 必要字段检查（比普通模式更严格）
required_fields = ['position_id', 'member_id', 'contract_name', 'side', 'deal_vol', 'deal_vol_usdt']
time_fields = ['timestamp', 'create_time', 'order_create_time']  # 支持多种时间字段

# 数据类型验证
if not pd.api.types.is_numeric_dtype(df['deal_vol_usdt']):
    df['deal_vol_usdt'] = pd.to_numeric(df['deal_vol_usdt'], errors='coerce')

# 数据量检查（性能保护）
if len(df) > 1000000:  # 100万条记录告警
    logger.warning("数据量过大，可能影响性能")
```

### 2. 🚀 **创建原始数据片段**（不进行智能补全）
```python
def _create_raw_position_fragments(self, df: pd.DataFrame):
    """
    从原始数据创建订单片段，基于真实交易记录，不进行任何推算
    """
    fragments = {}
    
    for position_id, group in df.groupby('position_id'):
        group_sorted = group.sort_values('create_time')
        
        # 分离开仓和平仓记录
        open_trades = group_sorted[group_sorted['side'].isin([1, 3])]
        close_trades = group_sorted[group_sorted['side'].isin([2, 4])]
        
        # 🚀 关键：只记录实际存在的数据，不进行推算
        fragment = CompletePosition(
            position_id=str(position_id),
            member_id=str(group_sorted.iloc[0]['member_id']),
            contract_name=str(group_sorted.iloc[0]['contract_name']),
            
            # 开仓信息：只记录实际存在的开仓数据
            first_open_time=open_trades.iloc[0]['create_time'] if len(open_trades) > 0 else None,
            total_open_amount=open_trades['deal_vol_usdt'].sum() if len(open_trades) > 0 else 0.0,
            open_trades_count=len(open_trades),
            
            # 平仓信息：只记录实际存在的平仓数据  
            first_close_time=close_trades.iloc[0]['create_time'] if len(close_trades) > 0 else None,
            total_close_amount=close_trades['deal_vol_usdt'].sum() if len(close_trades) > 0 else 0.0,
            close_trades_count=len(close_trades),
            
            # 🚀 关键：增量模式下所有片段都标记为不完整，等待与历史真实数据补齐
            is_completed=False,
            
            # 真实盈亏（不推算）
            real_profit=group_sorted.get('profit', pd.Series([0.0])).sum(),
        )
        
        fragments[str(position_id)] = fragment
    
    return fragments
```

### 3. 🎯 **严格的完整性判断**
```python
def _is_position_truly_completed(self, position: CompletePosition) -> bool:
    """
    基于deal_vol精确匹配的完整性判断逻辑
    """
    
    # 1. 基础检查：是否有平仓记录
    if position.close_trades_count == 0 or position.total_close_amount == 0:
        return False
    
    # 2. 开仓数量有效性检查
    if position.total_open_amount <= 0:
        return False
    
    # 3. 🎯 精确数量匹配判断（多层次容差）
    open_amount = position.total_open_amount
    close_amount = position.total_close_amount
    vol_diff = abs(open_amount - close_amount)
    
    # 第一层：绝对精确匹配（0.0001 USDT）
    absolute_tolerance = 0.0001
    if vol_diff <= absolute_tolerance:
        return True
    
    # 第二层：相对容差匹配（0.1%）
    relative_tolerance = 0.001
    relative_diff = vol_diff / open_amount
    if relative_diff <= relative_tolerance:
        return True
    
    # 第三层：部分平仓判断
    if close_amount < open_amount:
        completion_ratio = close_amount / open_amount
        # 95%以上完成度认为接近完整
        if completion_ratio >= 0.95:
            return True
        else:
            return False  # 明确的部分平仓
    
    # 第四层：过度平仓处理
    else:  # close_amount > open_amount
        over_ratio = close_amount / open_amount
        if over_ratio > 1.1:  # 超过110%认为异常但完整
            return True
        else:  # 轻微过度平仓认为完整
            return True
```

### 4. 🔄 **等待表补全机制**

#### 4.1 从等待表读取历史数据
```python
def _get_waiting_positions(self) -> List[Dict]:
    """获取等待中的订单"""
    sql = """
    SELECT position_id, member_id, contract_name, primary_side,
           first_open_time, total_open_amount, open_trades_count,
           avg_open_price, total_open_volume, waiting_since,
           last_check_time, check_count
    FROM incomplete_positions_waiting
    ORDER BY waiting_since ASC
    """
    return self.db_manager.execute_sql(sql)
```

#### 4.2 精确匹配算法
```python
def _find_matching_complete_position(self, waiting_pos: dict, new_positions: Dict):
    """
    精确匹配算法：基于position_id完全匹配查找完整订单
    """
    
    # position_id完全匹配（由于position_id生成逻辑统一可靠）
    if waiting_pos['position_id'] in new_positions:
        new_pos = new_positions[waiting_pos['position_id']]
        
        # 检查新订单是否完整
        if self._is_position_truly_completed(new_pos):
            # 进一步验证数量匹配
            if self._validate_quantity_match(waiting_pos, new_pos):
                return new_pos
    
    return None
```

#### 4.3 历史数据与新数据合并
```python
def _merge_waiting_and_new_data(self, waiting_pos: dict, new_pos: CompletePosition):
    """
    合并历史开仓数据和新的完整数据
    """
    
    # 🚀 关键：使用历史真实开仓数据 + 新的平仓数据
    merged_position = CompletePosition(
        position_id=waiting_pos['position_id'],
        member_id=waiting_pos['member_id'],
        contract_name=waiting_pos['contract_name'],
        
        # 开仓信息：使用等待表中的历史真实数据
        first_open_time=waiting_pos['first_open_time'],
        total_open_amount=waiting_pos['total_open_amount'],
        open_trades_count=waiting_pos['open_trades_count'],
        avg_open_price=waiting_pos['avg_open_price'],
        
        # 平仓信息：使用新数据中的平仓信息
        first_close_time=new_pos.first_close_time,
        total_close_amount=new_pos.total_close_amount,
        close_trades_count=new_pos.close_trades_count,
        avg_close_price=new_pos.avg_close_price,
        
        # 标记为完整
        is_completed=True,
        
        # 重新计算持仓时长和盈亏
        total_duration_minutes=(new_pos.last_close_time - waiting_pos['first_open_time']).total_seconds() / 60,
        real_profit=new_pos.real_profit,
    )
    
    return merged_position
```

### 5. 等待表管理

#### 5.1 添加不完整订单到等待表
```python
def _add_to_waiting_table(self, incomplete_positions: Dict):
    """添加新的不完整订单到等待表"""
    
    sql = """
    INSERT OR REPLACE INTO incomplete_positions_waiting 
    (position_id, member_id, contract_name, primary_side, first_open_time, 
     total_open_amount, open_trades_count, avg_open_price, total_open_volume,
     waiting_since, last_check_time, check_count, source_task_id, data_version,
     created_at, updated_at)
    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    """
    
    # 批量插入不完整订单
    self.db_manager.execute_many(sql, insert_data)
```

#### 5.2 移除已补全订单
```python
def _remove_from_waiting_table(self, completed_ids: List[str]):
    """从等待表移除已补全的订单"""
    
    if completed_ids:
        placeholders = ','.join(['?' for _ in completed_ids])
        sql = f"DELETE FROM incomplete_positions_waiting WHERE position_id IN ({placeholders})"
        self.db_manager.execute_sql(sql, completed_ids)
```

#### 5.3 定期清理机制
```python
def cleanup_old_waiting_positions(self, max_waiting_days=30, max_check_count=100):
    """清理长期未匹配的等待订单"""
    
    cutoff_date = datetime.now() - timedelta(days=max_waiting_days)
    
    sql = """
    DELETE FROM incomplete_positions_waiting 
    WHERE waiting_since < ? OR check_count > ?
    """
    
    self.db_manager.execute_sql(sql, [cutoff_date, max_check_count])
```

## 🎯 **数据处理策略**

### 对于不同类型的数据：

#### 1. **完整数据**（有开仓+平仓，且数量匹配）
- ✅ 直接标记为完整，进入分析流程
- ✅ 保持数据的真实性

#### 2. **只有开仓数据**（缺失平仓）
- 📝 添加到等待表
- ⏳ 等待后续批次的平仓数据
- 🔄 通过position_id精确匹配补全

#### 3. **只有平仓数据**（缺失开仓）
- 🔍 检查等待表中是否有对应的开仓记录
- ✅ 如果找到，合并为完整订单
- ❌ 如果没找到，标记为异常数据

#### 4. **部分平仓数据**（开仓>平仓，完成度<95%）
- 📝 添加到等待表
- ⏳ 等待后续的剩余平仓数据

## 🎯 **优势特点**

### ✅ **数据真实性**
- 不进行任何推算或估算
- 保持原始数据的完整性
- 避免引入虚假信息

### ✅ **精确匹配**
- 基于position_id的精确匹配
- 多层次容差判断
- 严格的完整性标准

### ✅ **历史数据利用**
- 通过等待表机制利用历史数据
- 跨批次数据补全
- 真实的时间跨度分析

## ⚠️ **潜在限制**

### 1. **数据利用率**
- 对不完整数据的利用率较低
- 需要等待后续数据才能完成分析

### 2. **等待表管理**
- 需要维护等待表的状态
- 长期未匹配数据的清理策略

### 3. **实时性**
- 分析结果可能延迟
- 需要多次处理才能获得完整结果
