#!/usr/bin/env python3
"""
检查最近任务的处理模式
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import <PERSON><PERSON><PERSON>ana<PERSON>

def check_processing_modes():
    """检查最近任务的处理模式"""
    
    db = DuckDBManager()
    
    print("=== 检查最近任务的处理模式 ===")
    
    # 检查tasks表中的处理模式信息
    try:
        tasks = db.execute_query('''
        SELECT task_id, task_type, filename, status, created_at, 
               CASE 
                   WHEN task_data LIKE '%incremental%' THEN 'incremental'
                   WHEN task_data LIKE '%normal%' THEN 'normal'
                   ELSE 'unknown'
               END as processing_mode
        FROM tasks 
        ORDER BY created_at DESC 
        LIMIT 5
        ''')
        
        print("最近的任务:")
        for task in tasks:
            print(f"  {task[0][:8]}...: {task[1]}, {task[2]}, {task[3]}, {task[4]}, 模式: {task[5]}")
            
    except Exception as e:
        print(f"查询tasks表失败: {e}")
    
    # 检查position_analysis表中是否有processing_mode字段
    print("\n=== 检查position_analysis表结构 ===")
    try:
        columns = db.execute_query("DESCRIBE position_analysis")
        print("position_analysis表的字段:")
        for col in columns:
            print(f"  {col[0]}: {col[1]}")
            
        # 检查是否有processing_mode相关的数据
        if any('processing_mode' in str(col[0]).lower() for col in columns):
            print("\n检查processing_mode字段的值分布:")
            mode_dist = db.execute_query('''
            SELECT processing_mode, COUNT(*) as count 
            FROM position_analysis 
            GROUP BY processing_mode
            ''')
            for row in mode_dist:
                print(f"  {row[0]}: {row[1]} 条记录")
        else:
            print("position_analysis表中没有processing_mode字段")
            
    except Exception as e:
        print(f"查询表结构失败: {e}")
    
    # 检查等待表的创建时间和最后更新时间
    print("\n=== 检查等待表状态 ===")
    try:
        # 检查表是否存在
        tables = db.execute_query("SHOW TABLES")
        table_names = [table[0] for table in tables]
        
        if 'incomplete_positions_waiting' in table_names:
            print("等待表存在")
            
            # 检查表结构
            waiting_columns = db.execute_query("DESCRIBE incomplete_positions_waiting")
            print("等待表字段:")
            for col in waiting_columns:
                print(f"  {col[0]}: {col[1]}")
                
            # 检查是否有数据
            count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting")[0][0]
            print(f"等待表记录数: {count}")
            
            if count > 0:
                # 检查最新记录
                recent = db.execute_query('''
                SELECT position_id, member_id, contract_name, waiting_since, check_count
                FROM incomplete_positions_waiting 
                ORDER BY waiting_since DESC 
                LIMIT 3
                ''')
                print("最新的等待记录:")
                for row in recent:
                    print(f"  {row[0]}: {row[1]}, {row[2]}, 等待自{row[3]}, 检查{row[4]}次")
        else:
            print("等待表不存在")
            
    except Exception as e:
        print(f"检查等待表失败: {e}")

if __name__ == "__main__":
    check_processing_modes()
