#!/usr/bin/env python3
"""
调试增量处理中的完整性判断逻辑
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from database.duckdb_manager import DuckDBManager
from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition
from datetime import datetime

def test_completeness_logic():
    """测试完整性判断逻辑"""
    
    print("=== 增量处理完整性判断逻辑测试 ===")
    
    # 创建增量处理器
    processor = SimpleIncrementalProcessor()
    
    # 获取配置信息
    print(f"绝对容差: {processor.config.matching.tolerance.absolute}")
    print(f"相对容差: {processor.config.matching.tolerance.relative}")
    
    # 测试用例
    test_cases = [
        {
            'name': '完全匹配',
            'open_amount': 1000.0,
            'close_amount': 1000.0,
            'expected': True
        },
        {
            'name': '绝对容差内匹配',
            'open_amount': 1000.0,
            'close_amount': 999.9999,
            'expected': True
        },
        {
            'name': '相对容差内匹配',
            'open_amount': 1000000.0,
            'close_amount': 999000.0,  # 0.1%差异
            'expected': True
        },
        {
            'name': '部分平仓（明显不完整）',
            'open_amount': 1000.0,
            'close_amount': 500.0,
            'expected': False
        },
        {
            'name': '无平仓记录',
            'open_amount': 1000.0,
            'close_amount': 0.0,
            'expected': False
        },
        {
            'name': '过度平仓（可能异常）',
            'open_amount': 1000.0,
            'close_amount': 1500.0,
            'expected': True  # 根据当前逻辑，过度平仓被认为是完整的
        }
    ]
    
    print("\n=== 测试用例结果 ===")
    for case in test_cases:
        # 创建测试订单
        position = CompletePosition(
            position_id="test_001",
            member_id="test_user",
            contract_name="BTCUSDT",
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=case['open_amount'],
            total_open_volume=case['open_amount'] / 50000.0,  # 假设价格50000
            avg_open_price=50000.0,
            open_trades_count=1,
            primary_side=1,
            first_close_time=datetime.now() if case['close_amount'] > 0 else None,
            last_close_time=datetime.now() if case['close_amount'] > 0 else None,
            total_close_amount=case['close_amount'],
            total_close_volume=case['close_amount'] / 50000.0,  # 假设价格50000
            avg_close_price=50000.0,
            close_trades_count=1 if case['close_amount'] > 0 else 0,
            is_completed=case['close_amount'] > 0,
            total_duration_minutes=60,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=1,
            reduce_position_count=1 if case['close_amount'] > 0 else 0,
            risk_score=0.0,
            abnormal_flags=[],
            leverage=1.0,
            total_fee=0.0
        )
        
        # 测试完整性判断
        result = processor._is_position_truly_completed(position)
        status = "✅" if result == case['expected'] else "❌"
        
        print(f"{status} {case['name']}: 开仓{case['open_amount']}, 平仓{case['close_amount']}, "
              f"预期{case['expected']}, 实际{result}")

def check_real_data_completeness():
    """检查真实数据的完整性判断"""
    
    print("\n=== 真实数据完整性检查 ===")
    
    db = DuckDBManager()
    processor = SimpleIncrementalProcessor()
    
    # 获取一些不完整的订单样本
    incomplete_orders = db.execute_query('''
    SELECT position_id, member_id, contract_name, 
           total_open_amount, total_close_amount,
           ABS(total_close_amount - total_open_amount) as diff
    FROM position_analysis 
    WHERE ABS(total_close_amount - total_open_amount) > 0.01
       OR total_close_amount = 0
    ORDER BY diff DESC
    LIMIT 5
    ''')
    
    print(f"检查 {len(incomplete_orders)} 个可能不完整的订单:")
    
    for row in incomplete_orders:
        position_id, member_id, contract_name, open_amount, close_amount, diff = row
        
        # 创建CompletePosition对象进行测试
        position = CompletePosition(
            position_id=str(position_id),
            member_id=str(member_id),
            contract_name=str(contract_name),
            first_open_time=datetime.now(),
            last_open_time=datetime.now(),
            total_open_amount=float(open_amount),
            total_open_volume=float(open_amount) / 50000.0,  # 假设价格50000
            avg_open_price=50000.0,
            open_trades_count=1,
            primary_side=1,
            first_close_time=datetime.now() if close_amount > 0 else None,
            last_close_time=datetime.now() if close_amount > 0 else None,
            total_close_amount=float(close_amount),
            total_close_volume=float(close_amount) / 50000.0,  # 假设价格50000
            avg_close_price=50000.0,
            close_trades_count=1 if close_amount > 0 else 0,
            is_completed=close_amount > 0,
            total_duration_minutes=60,
            real_profit=0.0,
            calculated_profit=0.0,
            is_quick_trade=False,
            is_scalping=False,
            add_position_count=1,
            reduce_position_count=1 if close_amount > 0 else 0,
            risk_score=0.0,
            abnormal_flags=[],
            leverage=1.0,
            total_fee=0.0
        )
        
        # 测试完整性判断
        is_complete = processor._is_position_truly_completed(position)
        
        # 计算相对差异
        if open_amount > 0:
            relative_diff = abs(open_amount - close_amount) / open_amount
        else:
            relative_diff = float('inf')
        
        status = "完整" if is_complete else "不完整"
        print(f"  {position_id}: 开仓{open_amount:.2f}, 平仓{close_amount:.2f}, "
              f"差异{diff:.2f}, 相对差异{relative_diff:.4f}, 判断结果: {status}")

if __name__ == "__main__":
    test_completeness_logic()
    check_real_data_completeness()
