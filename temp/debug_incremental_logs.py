#!/usr/bin/env python3
"""
调试增量处理的日志和执行流程
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
import logging
from database.duckdb_manager import DuckDBManager
from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
from datetime import datetime

# 设置详细日志
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def create_real_incomplete_data():
    """基于真实数据创建测试用例"""
    
    # 从数据库获取一些真实的不完整订单数据
    db = DuckDBManager()
    
    try:
        # 获取一些明显不完整的订单的原始交易记录
        incomplete_samples = db.execute_query('''
        SELECT position_id, member_id, contract_name, 
               total_open_amount, total_close_amount
        FROM position_analysis 
        WHERE total_close_amount = 0 OR ABS(total_close_amount - total_open_amount) > 1000
        LIMIT 3
        ''')
        
        print("=== 基于真实数据创建测试用例 ===")
        test_data = []
        
        for i, (pos_id, member_id, contract_name, open_amt, close_amt) in enumerate(incomplete_samples):
            print(f"样本 {i+1}: {pos_id}, 开仓{open_amt}, 平仓{close_amt}")
            
            # 创建开仓记录
            test_data.append({
                'position_id': f'real_test_{i+1}',
                'member_id': str(member_id),
                'contract_name': str(contract_name),
                'side': 1,  # 开多
                'deal_vol_usdt': float(open_amt),
                'deal_vol': float(open_amt) / 50000.0,  # 假设价格
                'price': 50000.0,
                'create_time': '2025-08-01 10:00:00',
                'profit': 0.0
            })
            
            # 如果有部分平仓，添加平仓记录
            if close_amt > 0:
                test_data.append({
                    'position_id': f'real_test_{i+1}',
                    'member_id': str(member_id),
                    'contract_name': str(contract_name),
                    'side': 2,  # 平空
                    'deal_vol_usdt': float(close_amt),
                    'deal_vol': float(close_amt) / 50000.0,
                    'price': 50000.0,
                    'create_time': '2025-08-01 11:00:00',
                    'profit': 0.0
                })
        
        return pd.DataFrame(test_data)
        
    except Exception as e:
        print(f"获取真实数据失败: {e}")
        return pd.DataFrame()

def debug_incremental_processing():
    """详细调试增量处理流程"""
    
    print("=== 详细调试增量处理流程 ===")
    
    # 创建基于真实数据的测试用例
    test_df = create_real_incomplete_data()
    
    if test_df.empty:
        print("无法创建测试数据，退出")
        return
    
    print(f"测试数据: {len(test_df)} 条记录")
    print("数据详情:")
    for _, row in test_df.iterrows():
        print(f"  {row['position_id']}: {row['member_id']}, side={row['side']}, amount={row['deal_vol_usdt']}")
    
    # 清空等待表
    db = DuckDBManager()
    try:
        db.execute_sql("DELETE FROM incomplete_positions_waiting")
        print("\n清空等待表完成")
    except Exception as e:
        print(f"清空等待表失败: {e}")
    
    # 创建增量处理器，启用详细日志
    processor = SimpleIncrementalProcessor(task_id="debug_incremental_001")
    
    print(f"\n=== 开始详细调试 ===")
    
    try:
        # 逐步执行增量处理的各个阶段
        print("1. 数据验证...")
        if not processor._validate_input_data(test_df):
            print("❌ 数据验证失败")
            return
        print("✅ 数据验证通过")
        
        print("\n2. 创建订单片段...")
        new_complete_positions = processor._create_raw_position_fragments(test_df)
        print(f"✅ 创建了 {len(new_complete_positions)} 个订单片段")
        
        for pos_id, position in new_complete_positions.items():
            print(f"  {pos_id}: 开仓{position.total_open_amount}, 平仓{position.total_close_amount}, 平仓次数{position.close_trades_count}")
        
        print("\n3. 分离完整和不完整订单...")
        complete_positions, incomplete_positions = processor._separate_positions(new_complete_positions)
        print(f"✅ 完整订单: {len(complete_positions)}, 不完整订单: {len(incomplete_positions)}")
        
        print("完整订单:")
        for pos_id, position in complete_positions.items():
            print(f"  {pos_id}: 开仓{position.total_open_amount}, 平仓{position.total_close_amount}")
        
        print("不完整订单:")
        for pos_id, position in incomplete_positions.items():
            print(f"  {pos_id}: 开仓{position.total_open_amount}, 平仓{position.total_close_amount}")
        
        print("\n4. 尝试从等待表补全...")
        completed_from_waiting = processor._try_complete_waiting_positions(new_complete_positions)
        print(f"✅ 从等待表补全: {len(completed_from_waiting)} 个订单")
        
        print("\n5. 更新等待表...")
        print(f"准备添加到等待表的不完整订单数: {len(incomplete_positions)}")
        
        if incomplete_positions:
            print("即将添加到等待表的订单:")
            for pos_id, position in incomplete_positions.items():
                print(f"  {pos_id}: {position.member_id}, {position.contract_name}, 开仓{position.total_open_amount}")
        
        # 手动调用等待表更新
        processor._update_waiting_table(incomplete_positions, completed_from_waiting)
        print("✅ 等待表更新完成")
        
        print("\n6. 检查等待表结果...")
        waiting_count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting")[0][0]
        print(f"等待表记录数: {waiting_count}")
        
        if waiting_count > 0:
            waiting_records = db.execute_query('''
            SELECT position_id, member_id, contract_name, total_open_amount, waiting_since
            FROM incomplete_positions_waiting
            ORDER BY waiting_since DESC
            ''')
            
            print("等待表记录:")
            for record in waiting_records:
                print(f"  {record[0]}: {record[1]}, {record[2]}, 开仓{record[3]}, 等待自{record[4]}")
        else:
            print("⚠️ 等待表仍然为空！")
        
        # 获取处理统计
        stats = processor.get_stats()
        print(f"\n=== 处理统计 ===")
        print(f"总处理数: {stats['total_processed']}")
        print(f"从等待表补全: {stats['completed_from_waiting']}")
        print(f"新增不完整: {stats['new_incomplete']}")
        print(f"错误数: {stats['error_count']}")
        
    except Exception as e:
        print(f"❌ 调试过程中出现异常: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

def check_database_permissions():
    """检查数据库权限和连接"""
    
    print("\n=== 检查数据库权限和连接 ===")
    
    db = DuckDBManager()
    
    try:
        # 测试基本查询
        result = db.execute_query("SELECT 1 as test")
        print(f"✅ 基本查询成功: {result}")
        
        # 测试等待表查询
        count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting")[0][0]
        print(f"✅ 等待表查询成功，当前记录数: {count}")
        
        # 测试插入权限
        test_sql = """
        INSERT INTO incomplete_positions_waiting 
        (position_id, member_id, contract_name, primary_side, first_open_time, 
         total_open_amount, open_trades_count, avg_open_price, total_open_volume,
         waiting_since, last_check_time, check_count, source_task_id, data_version,
         created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        test_data = [
            'test_permission_001',
            'test_user',
            'TESTUSDT',
            1,
            datetime.now(),
            1000.0,
            1,
            50000.0,
            0.02,
            datetime.now(),
            datetime.now(),
            0,
            'test_task',
            '1.0',
            datetime.now(),
            datetime.now()
        ]
        
        db.execute_sql(test_sql, test_data)
        print("✅ 插入权限测试成功")
        
        # 验证插入
        verify_count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting WHERE position_id = 'test_permission_001'")[0][0]
        print(f"✅ 插入验证成功，测试记录数: {verify_count}")
        
        # 清理测试数据
        db.execute_sql("DELETE FROM incomplete_positions_waiting WHERE position_id = 'test_permission_001'")
        print("✅ 测试数据清理完成")
        
    except Exception as e:
        print(f"❌ 数据库权限检查失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

if __name__ == "__main__":
    check_database_permissions()
    debug_incremental_processing()
