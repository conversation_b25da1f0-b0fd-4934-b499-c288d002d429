#!/usr/bin/env python3
"""
测试增量处理逻辑，找出为什么不完整订单没有进入等待表
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
from database.duckdb_manager import DuckDBManager
from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
from datetime import datetime

def create_test_data():
    """创建测试数据，包含明显不完整的订单"""
    
    test_data = [
        # 完整订单 - 开仓和平仓匹配
        {
            'position_id': 'test_complete_001',
            'member_id': 'user001',
            'contract_name': 'BTCUSDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 1000.0,
            'deal_vol': 0.02,
            'price': 50000.0,
            'create_time': '2025-08-01 10:00:00',
            'profit': 0.0
        },
        {
            'position_id': 'test_complete_001',
            'member_id': 'user001', 
            'contract_name': 'BTCUSDT',
            'side': 2,  # 平空（对应开多）
            'deal_vol_usdt': 1000.0,
            'deal_vol': 0.02,
            'price': 50000.0,
            'create_time': '2025-08-01 11:00:00',
            'profit': 0.0
        },
        
        # 不完整订单1 - 只有开仓，没有平仓
        {
            'position_id': 'test_incomplete_001',
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'side': 3,  # 开空
            'deal_vol_usdt': 2000.0,
            'deal_vol': 0.5,
            'price': 4000.0,
            'create_time': '2025-08-01 12:00:00',
            'profit': 0.0
        },
        
        # 不完整订单2 - 部分平仓
        {
            'position_id': 'test_partial_001',
            'member_id': 'user003',
            'contract_name': 'ADAUSDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 3000.0,
            'deal_vol': 1000.0,
            'price': 3.0,
            'create_time': '2025-08-01 13:00:00',
            'profit': 0.0
        },
        {
            'position_id': 'test_partial_001',
            'member_id': 'user003',
            'contract_name': 'ADAUSDT', 
            'side': 2,  # 平空（对应开多）
            'deal_vol_usdt': 1500.0,  # 只平了一半
            'deal_vol': 500.0,
            'price': 3.0,
            'create_time': '2025-08-01 14:00:00',
            'profit': 0.0
        }
    ]
    
    return pd.DataFrame(test_data)

def test_incremental_processing():
    """测试增量处理逻辑"""
    
    print("=== 增量处理逻辑测试 ===")
    
    # 创建测试数据
    test_df = create_test_data()
    print(f"创建测试数据: {len(test_df)} 条记录")
    print("测试数据概览:")
    for _, row in test_df.iterrows():
        print(f"  {row['position_id']}: {row['member_id']}, side={row['side']}, amount={row['deal_vol_usdt']}")
    
    # 清空等待表（测试前清理）
    db = DuckDBManager()
    try:
        db.execute_sql("DELETE FROM incomplete_positions_waiting")
        print("\n清空等待表完成")
    except Exception as e:
        print(f"清空等待表失败: {e}")
    
    # 创建增量处理器
    processor = SimpleIncrementalProcessor(task_id="test_incremental_001")
    
    print(f"\n=== 开始增量处理 ===")
    print(f"配置信息:")
    print(f"  绝对容差: {processor.config.matching.tolerance.absolute}")
    print(f"  相对容差: {processor.config.matching.tolerance.relative}")
    
    # 执行增量处理
    try:
        complete_positions = processor.process_new_data(test_df)
        
        print(f"\n=== 处理结果 ===")
        print(f"完整订单数: {len(complete_positions)}")
        
        for pos_id, position in complete_positions.items():
            print(f"  {pos_id}: 开仓{position.total_open_amount}, 平仓{position.total_close_amount}")
        
        # 检查等待表
        print(f"\n=== 等待表检查 ===")
        waiting_count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting")[0][0]
        print(f"等待表记录数: {waiting_count}")
        
        if waiting_count > 0:
            waiting_records = db.execute_query('''
            SELECT position_id, member_id, contract_name, total_open_amount, waiting_since
            FROM incomplete_positions_waiting
            ORDER BY waiting_since DESC
            ''')
            
            print("等待表记录:")
            for record in waiting_records:
                print(f"  {record[0]}: {record[1]}, {record[2]}, 开仓{record[3]}, 等待自{record[4]}")
        
        # 获取处理统计
        stats = processor.get_stats()
        print(f"\n=== 处理统计 ===")
        print(f"总处理数: {stats['total_processed']}")
        print(f"从等待表补全: {stats['completed_from_waiting']}")
        print(f"新增不完整: {stats['new_incomplete']}")
        print(f"错误数: {stats['error_count']}")
        
    except Exception as e:
        print(f"增量处理失败: {e}")
        import traceback
        print(f"详细错误: {traceback.format_exc()}")

def check_position_completeness():
    """检查订单完整性判断逻辑"""
    
    print("\n=== 订单完整性判断测试 ===")
    
    processor = SimpleIncrementalProcessor()
    
    # 从真实数据中获取一些样本进行测试
    db = DuckDBManager()
    
    try:
        # 获取一些明显不完整的订单
        incomplete_samples = db.execute_query('''
        SELECT position_id, member_id, contract_name, 
               total_open_amount, total_close_amount, close_trades_count
        FROM position_analysis 
        WHERE total_close_amount = 0 OR ABS(total_close_amount - total_open_amount) > 100
        LIMIT 3
        ''')
        
        print("真实数据样本测试:")
        for sample in incomplete_samples:
            pos_id, member_id, contract_name, open_amt, close_amt, close_count = sample
            
            # 创建CompletePosition对象进行测试
            from modules.contract_risk_analysis.optimizers.position_based_optimizer import CompletePosition
            
            position = CompletePosition(
                position_id=str(pos_id),
                member_id=str(member_id),
                contract_name=str(contract_name),
                first_open_time=datetime.now(),
                last_open_time=datetime.now(),
                total_open_amount=float(open_amt),
                total_open_volume=float(open_amt) / 50000.0,
                avg_open_price=50000.0,
                open_trades_count=1,
                primary_side=1,
                first_close_time=datetime.now() if close_amt > 0 else None,
                last_close_time=datetime.now() if close_amt > 0 else None,
                total_close_amount=float(close_amt),
                total_close_volume=float(close_amt) / 50000.0,
                avg_close_price=50000.0,
                close_trades_count=int(close_count),
                is_completed=close_amt > 0,
                total_duration_minutes=60,
                real_profit=0.0,
                calculated_profit=0.0,
                is_quick_trade=False,
                is_scalping=False,
                add_position_count=1,
                reduce_position_count=1 if close_amt > 0 else 0,
                risk_score=0.0,
                abnormal_flags=[],
                leverage=1.0,
                total_fee=0.0
            )
            
            # 测试完整性判断
            is_complete = processor._is_position_truly_completed(position)
            
            print(f"  {pos_id}: 开仓{open_amt}, 平仓{close_amt}, 平仓次数{close_count}, 判断结果: {'完整' if is_complete else '不完整'}")
            
    except Exception as e:
        print(f"真实数据测试失败: {e}")

if __name__ == "__main__":
    test_incremental_processing()
    check_position_completeness()
