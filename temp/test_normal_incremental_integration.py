#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试普通模式和增量模式的完整协作流程
验证普通模式保存的不完整订单能被增量模式正确匹配
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'backend'))

def test_normal_incremental_integration():
    """测试普通模式和增量模式的完整协作"""
    
    print("🚀 开始测试普通模式和增量模式协作...")
    
    try:
        # 导入必要的模块
        from backend.modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer
        from backend.modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
        from database.duckdb_manager import DuckDBManager as DatabaseManager
        
        # 初始化
        optimizer = PositionBasedOptimizer()
        processor = SimpleIncrementalProcessor()
        db_manager = DatabaseManager()
        
        print("✅ 模块导入成功")
        
        # 清空等待表
        try:
            db_manager.execute_sql("DELETE FROM incomplete_positions_waiting")
            print("🧹 清空等待表")
        except Exception as e:
            print(f"⚠️  清空等待表失败: {e}")
        
        # === 第一阶段：普通模式处理（保存不完整订单） ===
        print("\n📊 第一阶段：普通模式处理...")
        
        base_time = datetime.now() - timedelta(hours=2)
        normal_data = []
        
        # 1. 完整订单
        normal_data.extend([
            {
                'position_id': 'INTEGRATION_COMPLETE_001',
                'member_id': 'user001',
                'contract_name': 'BTCUSDT',
                'side': 1,  # 开多
                'deal_vol_usdt': 1000.0,
                'timestamp': base_time,
                'profit': 0.0
            },
            {
                'position_id': 'INTEGRATION_COMPLETE_001',
                'member_id': 'user001',
                'contract_name': 'BTCUSDT',
                'side': 4,  # 平多
                'deal_vol_usdt': 1000.0,
                'timestamp': base_time + timedelta(minutes=30),
                'profit': 50.0
            }
        ])
        
        # 2. 不完整订单（只有开仓，等待增量模式补全）
        normal_data.append({
            'position_id': 'INTEGRATION_INCOMPLETE_002',
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'side': 3,  # 开空
            'deal_vol_usdt': 2000.0,
            'timestamp': base_time + timedelta(minutes=10),
            'profit': 0.0
        })
        
        normal_df = pd.DataFrame(normal_data)
        print(f"   创建普通模式数据: {len(normal_data)} 条记录")
        
        # 执行普通模式处理
        normal_positions = optimizer.build_complete_positions(normal_df)
        print(f"   普通模式处理完成: {len(normal_positions)} 个订单")
        
        # 检查等待表状态
        waiting_sql = "SELECT position_id, member_id FROM incomplete_positions_waiting"
        waiting_results = db_manager.execute_sql(waiting_sql)
        print(f"   等待表记录数: {len(waiting_results)}")
        for record in waiting_results:
            print(f"     - {record['position_id']}: 用户={record['member_id']}")
        
        # === 第二阶段：增量模式处理（匹配等待表数据） ===
        print("\n🔄 第二阶段：增量模式处理...")
        
        incremental_time = datetime.now() - timedelta(minutes=30)
        incremental_data = []
        
        # 新的完整订单
        incremental_data.extend([
            {
                'position_id': 'INTEGRATION_NEW_003',
                'member_id': 'user003',
                'contract_name': 'ADAUSDT',
                'side': 1,  # 开多
                'deal_vol_usdt': 1500.0,
                'deal_vol': 1500.0,  # 增量模式需要这个字段
                'timestamp': incremental_time,
                'create_time': incremental_time,  # 增量模式需要这个字段
                'profit': 0.0
            },
            {
                'position_id': 'INTEGRATION_NEW_003',
                'member_id': 'user003',
                'contract_name': 'ADAUSDT',
                'side': 4,  # 平多
                'deal_vol_usdt': 1500.0,
                'deal_vol': 1500.0,  # 增量模式需要这个字段
                'timestamp': incremental_time + timedelta(minutes=15),
                'create_time': incremental_time + timedelta(minutes=15),  # 增量模式需要这个字段
                'profit': 30.0
            }
        ])

        # 关键：为等待表中的订单提供平仓数据
        incremental_data.append({
            'position_id': 'INTEGRATION_INCOMPLETE_002',  # 匹配等待表中的订单
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 2000.0,
            'deal_vol': 2000.0,  # 增量模式需要这个字段
            'timestamp': incremental_time + timedelta(minutes=20),
            'create_time': incremental_time + timedelta(minutes=20),  # 增量模式需要这个字段
            'profit': -50.0
        })
        
        incremental_df = pd.DataFrame(incremental_data)
        print(f"   创建增量模式数据: {len(incremental_data)} 条记录")
        print(f"     - 新完整订单: INTEGRATION_NEW_003")
        print(f"     - 补全等待订单: INTEGRATION_INCOMPLETE_002 (平仓数据)")
        
        # 执行增量模式处理
        incremental_result = processor.process_new_data(incremental_df)
        print(f"   增量模式处理完成: {len(incremental_result)} 个完整订单")
        
        # 分析结果
        complete_from_waiting = 0
        new_complete = 0
        for pos_id, position in incremental_result.items():
            if pos_id == 'INTEGRATION_INCOMPLETE_002':
                complete_from_waiting += 1
                print(f"     ✅ 从等待表补全: {pos_id} (开仓={position.total_open_amount}, 平仓={position.total_close_amount})")
            elif pos_id == 'INTEGRATION_NEW_003':
                new_complete += 1
                print(f"     ✅ 新完整订单: {pos_id} (开仓={position.total_open_amount}, 平仓={position.total_close_amount})")
        
        # 检查等待表状态（应该减少）
        waiting_results_after = db_manager.execute_sql(waiting_sql)
        print(f"   处理后等待表记录数: {len(waiting_results_after)}")
        
        # === 验证结果 ===
        print(f"\n🎯 验证协作结果:")
        print(f"   普通模式保存到等待表: {len(waiting_results)} 个订单")
        print(f"   增量模式完成订单: {len(incremental_result)} 个")
        print(f"   从等待表补全: {complete_from_waiting} 个")
        print(f"   新完整订单: {new_complete} 个")
        print(f"   处理后等待表剩余: {len(waiting_results_after)} 个")
        
        # 成功条件
        success_conditions = [
            len(waiting_results) > 0,  # 普通模式保存了不完整订单
            complete_from_waiting > 0,  # 增量模式成功补全了等待表订单
            len(waiting_results_after) < len(waiting_results),  # 等待表记录减少
            'INTEGRATION_INCOMPLETE_002' in incremental_result  # 关键订单被成功补全
        ]
        
        if all(success_conditions):
            print("✅ 协作测试成功！")
            print("   - 普通模式正确保存不完整订单到等待表")
            print("   - 增量模式成功从等待表匹配并补全订单")
            print("   - 跨批次数据补全功能正常工作")
            return True
        else:
            print("❌ 协作测试失败")
            print(f"   成功条件: {success_conditions}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    test_normal_incremental_integration()
