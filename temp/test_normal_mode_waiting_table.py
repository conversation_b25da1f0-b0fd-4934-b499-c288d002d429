#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试普通模式是否正确保存不完整订单到等待表
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'backend'))

def test_normal_mode_waiting_table():
    """测试普通模式保存不完整订单到等待表"""
    
    print("🚀 开始测试普通模式等待表功能...")
    
    try:
        # 导入必要的模块
        from backend.modules.contract_risk_analysis.optimizers.position_based_optimizer import PositionBasedOptimizer
        from database.duckdb_manager import DuckDBManager as DatabaseManager
        
        # 初始化
        optimizer = PositionBasedOptimizer()
        db_manager = DatabaseManager()
        
        print("✅ 模块导入成功")
        
        # 清空等待表
        try:
            db_manager.execute_sql("DELETE FROM incomplete_positions_waiting")
            print("🧹 清空等待表")
        except Exception as e:
            print(f"⚠️  清空等待表失败: {e}")
        
        # 创建测试数据
        base_time = datetime.now() - timedelta(hours=1)
        test_data = []
        
        # 1. 完整订单（有开仓+平仓）
        test_data.extend([
            {
                'position_id': 'NORMAL_COMPLETE_001',
                'member_id': 'user001',
                'contract_name': 'BTCUSDT',
                'side': 1,  # 开多
                'deal_vol_usdt': 1000.0,
                'timestamp': base_time,
                'profit': 50.0
            },
            {
                'position_id': 'NORMAL_COMPLETE_001',
                'member_id': 'user001',
                'contract_name': 'BTCUSDT',
                'side': 4,  # 平多
                'deal_vol_usdt': 1000.0,
                'timestamp': base_time + timedelta(minutes=30),
                'profit': 50.0
            }
        ])
        
        # 2. 不完整订单（只有开仓）
        test_data.append({
            'position_id': 'NORMAL_INCOMPLETE_002',
            'member_id': 'user002',
            'contract_name': 'ETHUSDT',
            'side': 3,  # 开空
            'deal_vol_usdt': 2000.0,
            'timestamp': base_time + timedelta(minutes=10),
            'profit': 0.0
        })
        
        # 3. 不完整订单（只有平仓）
        test_data.append({
            'position_id': 'NORMAL_INCOMPLETE_003',
            'member_id': 'user003',
            'contract_name': 'ADAUSDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 1500.0,
            'timestamp': base_time + timedelta(minutes=20),
            'profit': -30.0
        })
        
        test_df = pd.DataFrame(test_data)
        print(f"📊 创建测试数据: {len(test_data)} 条记录")
        print(f"   - 完整订单: NORMAL_COMPLETE_001 (开仓+平仓)")
        print(f"   - 不完整订单1: NORMAL_INCOMPLETE_002 (只有开仓)")
        print(f"   - 不完整订单2: NORMAL_INCOMPLETE_003 (只有平仓)")
        
        # 执行普通模式处理
        print("\n🔄 执行普通模式处理...")
        complete_positions = optimizer.build_complete_positions(test_df)
        print(f"✅ 普通模式处理完成，返回订单数量: {len(complete_positions)}")
        
        # 打印结果详情
        for pos_id, position in complete_positions.items():
            print(f"   - {pos_id}: 完整={position.is_completed}, 开仓={position.total_open_amount}, 平仓={position.total_close_amount}")
        
        # 检查等待表状态
        print("\n📋 检查等待表状态...")
        waiting_sql = "SELECT position_id, member_id, total_open_amount, waiting_since FROM incomplete_positions_waiting ORDER BY position_id"
        waiting_results = db_manager.execute_sql(waiting_sql)
        
        print(f"等待表记录数: {len(waiting_results)}")
        for record in waiting_results:
            print(f"   - {record['position_id']}: 用户={record['member_id']}, 开仓金额={record['total_open_amount']:.2f}, 等待时间={record['waiting_since']}")
        
        # 验证结果
        print(f"\n🎯 验证结果:")
        expected_waiting = ['NORMAL_INCOMPLETE_002', 'NORMAL_INCOMPLETE_003']  # 不完整订单都应该在等待表中
        expected_complete = ['NORMAL_COMPLETE_001']   # 完整订单应该直接处理
        
        actual_waiting = [record['position_id'] for record in waiting_results]
        actual_complete = [pos_id for pos_id, pos in complete_positions.items() if pos.is_completed]
        actual_incomplete = [pos_id for pos_id, pos in complete_positions.items() if not pos.is_completed]
        
        print(f"   预期等待订单: {expected_waiting}")
        print(f"   实际等待订单: {actual_waiting}")
        print(f"   预期完整订单: {expected_complete}")
        print(f"   实际完整订单: {actual_complete}")
        print(f"   实际不完整订单: {actual_incomplete}")
        
        waiting_correct = set(expected_waiting) == set(actual_waiting)
        complete_correct = set(expected_complete) == set(actual_complete)
        
        if waiting_correct and complete_correct:
            print("✅ 修复成功！普通模式正确保存不完整订单到等待表")
            print("   - 完整订单正常处理")
            print("   - 只有开仓数据的订单保存到等待表")
            print("   - 只有平仓数据的订单经智能补全后也保存到等待表（为增量模式提供更多匹配数据）")
            return True
        else:
            if not waiting_correct:
                print("❌ 等待表状态不符合预期")
            if not complete_correct:
                print("❌ 完整订单处理不符合预期")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    test_normal_mode_waiting_table()
