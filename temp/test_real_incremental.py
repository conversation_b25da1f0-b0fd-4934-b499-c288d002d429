#!/usr/bin/env python3
"""
测试真实的增量处理流程，模拟用户的实际使用场景
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

import pandas as pd
import logging
from database.duckdb_manager import DuckDBManager

# 设置详细日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')

def create_real_data_sample():
    """从真实数据中提取样本进行测试"""
    
    db = DuckDBManager()
    
    try:
        # 从原始交易数据中获取一些样本
        # 注意：这里我们需要从原始交易表获取数据，而不是position_analysis表
        # 因为position_analysis可能已经被PositionBasedOptimizer处理过了
        
        print("=== 尝试从原始交易数据获取样本 ===")
        
        # 检查是否有原始交易数据表
        tables = db.execute_query("SHOW TABLES")
        table_names = [table[0] for table in tables]
        print(f"可用的表: {table_names}")
        
        # 寻找可能的原始数据表
        possible_tables = ['contract_trades', 'raw_trades', 'trade_data', 'original_data']
        raw_table = None
        
        for table in possible_tables:
            if table in table_names:
                raw_table = table
                break
        
        if raw_table:
            print(f"找到原始数据表: {raw_table}")
            # 从原始表获取样本
            sample_data = db.execute_query(f"SELECT * FROM {raw_table} LIMIT 10")
            print(f"原始数据样本: {len(sample_data)} 条记录")
            
            # 获取列名
            columns = db.execute_query(f"DESCRIBE {raw_table}")
            column_names = [col[0] for col in columns]
            print(f"列名: {column_names}")
            
            # 转换为DataFrame
            df = pd.DataFrame(sample_data, columns=column_names)
            return df
        else:
            print("未找到原始数据表，创建模拟数据")
            return create_mock_incomplete_data()
            
    except Exception as e:
        print(f"获取真实数据失败: {e}")
        return create_mock_incomplete_data()

def create_mock_incomplete_data():
    """创建模拟的不完整数据"""
    
    print("=== 创建模拟不完整数据 ===")
    
    # 创建一些明显不完整的订单数据
    data = []
    
    # 订单1：只有开仓，无平仓
    data.extend([
        {
            'position_id': 'mock_incomplete_001',
            'member_id': 'user_001',
            'contract_name': 'BTC_USDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 10000.0,
            'deal_vol': 0.2,
            'price': 50000.0,
            'create_time': '2025-08-01 10:00:00',
            'profit': 0.0
        }
    ])
    
    # 订单2：部分平仓
    data.extend([
        {
            'position_id': 'mock_partial_001',
            'member_id': 'user_002',
            'contract_name': 'ETH_USDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 5000.0,
            'deal_vol': 1.5,
            'price': 3333.33,
            'create_time': '2025-08-01 10:00:00',
            'profit': 0.0
        },
        {
            'position_id': 'mock_partial_001',
            'member_id': 'user_002',
            'contract_name': 'ETH_USDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 2000.0,  # 只平了一部分
            'deal_vol': 0.6,
            'price': 3333.33,
            'create_time': '2025-08-01 11:00:00',
            'profit': 100.0
        }
    ])
    
    # 订单3：完整订单（用于对比）
    data.extend([
        {
            'position_id': 'mock_complete_001',
            'member_id': 'user_003',
            'contract_name': 'ADA_USDT',
            'side': 1,  # 开多
            'deal_vol_usdt': 1000.0,
            'deal_vol': 2000.0,
            'price': 0.5,
            'create_time': '2025-08-01 10:00:00',
            'profit': 0.0
        },
        {
            'position_id': 'mock_complete_001',
            'member_id': 'user_003',
            'contract_name': 'ADA_USDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 1000.0,  # 完全平仓
            'deal_vol': 2000.0,
            'price': 0.5,
            'create_time': '2025-08-01 11:00:00',
            'profit': 50.0
        }
    ])
    
    df = pd.DataFrame(data)
    print(f"创建了 {len(df)} 条模拟数据")
    print("数据概览:")
    for _, row in df.iterrows():
        print(f"  {row['position_id']}: {row['member_id']}, side={row['side']}, amount={row['deal_vol_usdt']}")
    
    return df

def test_incremental_processing_simple():
    """简化的增量处理测试"""

    print("=== 简化增量处理测试 ===")

    # 清空等待表
    db = DuckDBManager()
    try:
        db.execute_sql("DELETE FROM incomplete_positions_waiting")
        print("清空等待表完成")
    except Exception as e:
        print(f"清空等待表失败: {e}")

    # 获取测试数据
    test_df = create_mock_incomplete_data()

    print(f"\n测试数据: {len(test_df)} 条记录")

    try:
        print("\n=== 直接使用增量处理器测试 ===")

        # 直接导入和使用增量处理器
        from modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor

        # 创建增量处理器
        processor = SimpleIncrementalProcessor(task_id="test_simple_incremental")

        # 处理数据
        complete_positions = processor.process_new_data(test_df)

        print(f"\n=== 处理结果 ===")
        print(f"完整订单数量: {len(complete_positions)}")

        # 检查等待表
        waiting_count = db.execute_query("SELECT COUNT(*) FROM incomplete_positions_waiting")[0][0]
        print(f"等待表记录数: {waiting_count}")

        if waiting_count > 0:
            waiting_records = db.execute_query('''
            SELECT position_id, member_id, contract_name, total_open_amount, waiting_since
            FROM incomplete_positions_waiting
            ORDER BY waiting_since DESC
            LIMIT 10
            ''')

            print("等待表记录:")
            for record in waiting_records:
                print(f"  {record[0]}: {record[1]}, {record[2]}, 开仓{record[3]}, 等待自{record[4]}")
        else:
            print("⚠️ 等待表仍然为空！")

        # 获取处理统计
        stats = processor.get_stats()
        print(f"\n=== 处理统计 ===")
        print(f"总处理数: {stats['total_processed']}")
        print(f"从等待表补全: {stats['completed_from_waiting']}")
        print(f"新增不完整: {stats['new_incomplete']}")
        print(f"错误数: {stats['error_count']}")

    except Exception as e:
        print(f"❌ 增量处理测试失败: {e}")
        import traceback
        print(f"详细错误信息: {traceback.format_exc()}")

if __name__ == "__main__":
    test_incremental_processing_simple()
