#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
专门测试等待表匹配功能
"""

import sys
import os
import pandas as pd
from datetime import datetime, timedelta

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(project_root)
sys.path.append(os.path.join(project_root, 'backend'))

def test_waiting_table_matching():
    """测试等待表匹配功能"""
    
    print("🚀 开始测试等待表匹配功能...")
    
    try:
        # 导入必要的模块
        from backend.modules.contract_risk_analysis.services.incremental_processor import SimpleIncrementalProcessor
        from database.duckdb_manager import DuckDBManager as DatabaseManager
        
        # 初始化
        processor = SimpleIncrementalProcessor()
        db_manager = DatabaseManager()
        
        print("✅ 模块导入成功")
        
        # 清空等待表
        try:
            db_manager.execute_sql("DELETE FROM incomplete_positions_waiting")
            print("🧹 清空等待表")
        except Exception as e:
            print(f"⚠️  清空等待表失败: {e}")
        
        # 手动插入等待表数据（模拟普通模式保存的不完整订单）
        print("\n📋 手动插入等待表数据...")
        base_time = datetime.now() - timedelta(hours=1)
        
        insert_sql = """
        INSERT INTO incomplete_positions_waiting 
        (position_id, member_id, contract_name, primary_side, first_open_time, 
         total_open_amount, open_trades_count, avg_open_price, total_open_volume,
         waiting_since, last_check_time, check_count, source_task_id, data_version,
         created_at, updated_at)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        """
        
        waiting_data = [
            'WAITING_TEST_001',  # position_id
            'user001',           # member_id
            'BTCUSDT',          # contract_name
            3,                  # primary_side (开空)
            base_time,          # first_open_time
            2000.0,             # total_open_amount
            1,                  # open_trades_count
            50000.0,            # avg_open_price
            2000.0,             # total_open_volume
            base_time,          # waiting_since
            base_time,          # last_check_time
            0,                  # check_count
            'test_mode',        # source_task_id
            '1.0',              # data_version
            base_time,          # created_at
            base_time           # updated_at
        ]
        
        db_manager.execute_sql(insert_sql, waiting_data)
        print("✅ 等待表数据插入成功")
        
        # 验证等待表数据
        waiting_check = db_manager.execute_sql("SELECT position_id, member_id, total_open_amount FROM incomplete_positions_waiting")
        print(f"   等待表记录: {len(waiting_check)} 条")
        for record in waiting_check:
            print(f"     - {record['position_id']}: 用户={record['member_id']}, 开仓={record['total_open_amount']}")
        
        # 创建增量数据（提供平仓数据）
        print("\n🔄 创建增量数据（平仓数据）...")
        incremental_time = datetime.now() - timedelta(minutes=30)
        
        incremental_data = [{
            'position_id': 'WAITING_TEST_001',  # 匹配等待表中的订单
            'member_id': 'user001',
            'contract_name': 'BTCUSDT',
            'side': 2,  # 平空
            'deal_vol_usdt': 2000.0,
            'deal_vol': 2000.0,
            'timestamp': incremental_time,
            'create_time': incremental_time,
            'profit': 100.0
        }]
        
        incremental_df = pd.DataFrame(incremental_data)
        print(f"   增量数据: {len(incremental_data)} 条记录")
        print(f"     - 订单ID: WAITING_TEST_001")
        print(f"     - 操作: 平空")
        print(f"     - 金额: 2000.0")
        
        # 执行增量处理
        print("\n⚙️ 执行增量处理...")
        result = processor.process_new_data(incremental_df)
        
        print(f"✅ 增量处理完成: {len(result)} 个完整订单")
        
        # 分析结果
        for pos_id, position in result.items():
            print(f"   订单 {pos_id}:")
            print(f"     - 完整状态: {position.is_completed}")
            print(f"     - 开仓金额: {position.total_open_amount}")
            print(f"     - 平仓金额: {position.total_close_amount}")
            print(f"     - 开仓时间: {position.first_open_time}")
            print(f"     - 平仓时间: {position.first_close_time}")
            print(f"     - 盈亏: {position.real_profit}")
        
        # 检查等待表状态
        waiting_after = db_manager.execute_sql("SELECT position_id FROM incomplete_positions_waiting")
        print(f"\n📋 处理后等待表记录: {len(waiting_after)} 条")
        
        # 验证结果
        success = (
            len(result) > 0 and  # 有完整订单产生
            'WAITING_TEST_001' in result and  # 目标订单被处理
            result['WAITING_TEST_001'].is_completed and  # 订单被标记为完整
            result['WAITING_TEST_001'].total_open_amount > 0 and  # 有开仓数据
            result['WAITING_TEST_001'].total_close_amount > 0 and  # 有平仓数据
            len(waiting_after) < len(waiting_check)  # 等待表记录减少
        )
        
        if success:
            print("✅ 等待表匹配测试成功！")
            print("   - 成功从等待表获取历史开仓数据")
            print("   - 成功与新的平仓数据匹配")
            print("   - 生成完整的订单记录")
            print("   - 等待表记录正确清理")
            return True
        else:
            print("❌ 等待表匹配测试失败")
            print(f"   结果数量: {len(result)}")
            print(f"   目标订单存在: {'WAITING_TEST_001' in result}")
            if 'WAITING_TEST_001' in result:
                pos = result['WAITING_TEST_001']
                print(f"   订单完整: {pos.is_completed}")
                print(f"   开仓金额: {pos.total_open_amount}")
                print(f"   平仓金额: {pos.total_close_amount}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        print(traceback.format_exc())
        return False

if __name__ == "__main__":
    test_waiting_table_matching()
